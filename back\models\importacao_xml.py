from .escritorio import db
from sqlalchemy.sql import func

class ImportacaoXML(db.Model):
    __tablename__ = 'importacao_xml'
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    empresa_id = db.Column(db.<PERSON>ger, db.<PERSON><PERSON>('empresa.id'), nullable=False)
    escritorio_id = db.Column(db.Integer, db.Foreign<PERSON>ey('escritorio.id'), nullable=False)
    usuario_id = db.Column(db.Integer, db.ForeignKey('usuario.id'), nullable=False)
    arquivo_nome = db.Column(db.String(255), nullable=False)
    chave_nf = db.Column(db.String(50))
    numero_nf = db.Column(db.String(20))
    data_emissao = db.Column(db.Date)
    cnpj_emitente = db.Column(db.String(18))
    razao_social_emitente = db.Column(db.String(255))
    data_importacao = db.Column(db.DateTime, server_default=func.now())
    status = db.Column(db.String(20), default='concluido')
    mensagem = db.Column(db.Text)
    
    def __repr__(self):
        return f"<ImportacaoXML {self.id} - {self.arquivo_nome}>"
    
    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'usuario_id': self.usuario_id,
            'arquivo_nome': self.arquivo_nome,
            'chave_nf': self.chave_nf,
            'numero_nf': self.numero_nf,
            'data_emissao': self.data_emissao.isoformat() if self.data_emissao else None,
            'cnpj_emitente': self.cnpj_emitente,
            'razao_social_emitente': self.razao_social_emitente,
            'data_importacao': self.data_importacao.isoformat() if self.data_importacao else None,
            'status': self.status,
            'mensagem': self.mensagem
        }
