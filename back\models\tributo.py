from .escritorio import db
from sqlalchemy.sql import func

class Tributo(db.Model):
    __tablename__ = 'tributo'
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    empresa_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON>('empresa.id'), nullable=False)
    escritorio_id = db.<PERSON>umn(db.In<PERSON>ger, db.<PERSON>('escritorio.id'), nullable=False)
    cliente_id = db.Column(db.Integer, db.<PERSON>ey('cliente.id'), nullable=False)
    produto_id = db.Column(db.Integer, db.<PERSON>ey('produto.id'), nullable=False)
    data_emissao = db.Column(db.Date, nullable=False)
    data_saida = db.Column(db.Date)
    # Adicionar ao modelo Tributo
    nota_fiscal_item_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('nota_fiscal_item.id'), nullable=True)
    cenario_id = db.Column(db.Integer, nullable=True)
    cenario_tipo = db.Column(db.String(20), nullable=True)  # 'icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal'

    # ICMS
    icms_origem = db.Column(db.String(2))          # ORIGEM
    icms_cst = db.Column(db.String(3))              # CST
    icms_mod_bc = db.Column(db.String(2))
    icms_p_red_bc = db.Column(db.Numeric(10, 4))
    icms_vbc = db.Column(db.Numeric(10, 2))
    icms_aliquota = db.Column(db.Numeric(10, 4))
    icms_valor = db.Column(db.Numeric(10, 2))
    icms_v_op = db.Column(db.Numeric(10, 2))       # Novo campo para vICMSOp
    icms_p_dif = db.Column(db.Numeric(10, 4))      # Novo campo para pDif
    icms_v_dif = db.Column(db.Numeric(10, 2))      # Novo campo para vICMSDif

    # ICMS-ST
    icms_st_mod_bc = db.Column(db.String(2))       # modBCST
    icms_st_p_mva = db.Column(db.Numeric(10, 4))   # pMVAST
    icms_st_vbc = db.Column(db.Numeric(10, 2))     # vBCST
    icms_st_aliquota = db.Column(db.Numeric(10, 4)) # pICMSST
    icms_st_valor = db.Column(db.Numeric(10, 2))   # vICMSST

    # IPI
    ipi_cst = db.Column(db.String(3))
    ipi_vbc = db.Column(db.Numeric(10, 2))
    ipi_aliquota = db.Column(db.Numeric(10, 4))
    ipi_valor = db.Column(db.Numeric(10, 2))
    ipi_codigo_enquadramento = db.Column(db.String(3))  # Novo campo para cEnq
    ipi_ex = db.Column(db.String(3))  # Novo campo para EXTIPI

    # PIS
    pis_cst = db.Column(db.String(3))
    pis_vbc = db.Column(db.Numeric(10, 2))
    pis_aliquota = db.Column(db.Numeric(10, 4))
    pis_valor = db.Column(db.Numeric(10, 2))
    pis_p_red_bc = db.Column(db.Numeric(10, 4))  # Novo campo para percentual de redução da base de cálculo

    # COFINS
    cofins_cst = db.Column(db.String(3))
    cofins_vbc = db.Column(db.Numeric(10, 2))
    cofins_aliquota = db.Column(db.Numeric(10, 4))
    cofins_valor = db.Column(db.Numeric(10, 2))
    cofins_p_red_bc = db.Column(db.Numeric(10, 4))  # Novo campo para percentual de redução da base de cálculo

    # DIFAL (Diferencial de Alíquota)
    difal_vbc = db.Column(db.Numeric(10, 2))                  # vBC
    difal_p_fcp_uf_dest = db.Column(db.Numeric(10, 4))        # pFCPUFDest CALCULO
    difal_p_icms_uf_dest = db.Column(db.Numeric(10, 4))       # pICMSUFDest CALCULO
    difal_p_icms_inter = db.Column(db.Numeric(10, 4))         # pICMSInter CALCULO
    difal_p_icms_inter_part = db.Column(db.Numeric(10, 4))    # pICMSInterPart CALCULO
    difal_v_fcp_uf_dest = db.Column(db.Numeric(10, 2))        # vFCPUFDest
    difal_v_icms_uf_dest = db.Column(db.Numeric(10, 2))       # vICMSUFDest
    difal_v_icms_uf_remet = db.Column(db.Numeric(10, 2))      # vICMSUFRemet

    # Informações adicionais
    numero_nf = db.Column(db.String(20))
    chave_nf = db.Column(db.String(50))
    tipo_operacao = db.Column(db.String(1))  # 0 = entrada, 1 = saída (valor da tag tpNF do XML)
    status = db.Column(db.String(20), default='novo')

    # Valores do produto
    quantidade = db.Column(db.Numeric(10, 4))
    valor_unitario = db.Column(db.Numeric(10, 4))
    valor_total = db.Column(db.Numeric(10, 2))
    valor_frete = db.Column(db.Numeric(10, 2))  # Novo campo para frete proporcional
    valor_desconto = db.Column(db.Numeric(10, 2))  # Novo campo para desconto proporcional

    # Colunas para valores de auditoria
    cenario_icms_vbc = db.Column(db.Numeric(10, 2))
    cenario_icms_valor = db.Column(db.Numeric(10, 2))
    cenario_icms_st_vbc = db.Column(db.Numeric(10, 2))
    cenario_icms_st_valor = db.Column(db.Numeric(10, 2))
    cenario_ipi_vbc = db.Column(db.Numeric(10, 2))
    cenario_ipi_valor = db.Column(db.Numeric(10, 2))
    cenario_pis_vbc = db.Column(db.Numeric(10, 2))
    cenario_pis_valor = db.Column(db.Numeric(10, 2))
    cenario_cofins_vbc = db.Column(db.Numeric(10, 2))
    cenario_cofins_valor = db.Column(db.Numeric(10, 2))
    cenario_difal_valor = db.Column(db.Numeric(10, 2))

    # Colunas para referência aos cenários utilizados na auditoria
    cenario_icms_id = db.Column(db.Integer, db.ForeignKey('cenario_icms.id'))
    cenario_icms_st_id = db.Column(db.Integer, db.ForeignKey('cenario_icms_st.id'))
    cenario_ipi_id = db.Column(db.Integer, db.ForeignKey('cenario_ipi.id'))
    cenario_pis_id = db.Column(db.Integer, db.ForeignKey('cenario_pis.id'))
    cenario_cofins_id = db.Column(db.Integer, db.ForeignKey('cenario_cofins.id'))
    cenario_difal_id = db.Column(db.Integer, db.ForeignKey('cenario_difal.id'))

    # Status e data da auditoria geral (para compatibilidade)
    auditoria_status = db.Column(db.String(20), default='pendente')
    auditoria_data = db.Column(db.DateTime)

    # Status e data da auditoria específicos por tipo de tributo
    auditoria_icms_status = db.Column(db.String(20), default='pendente')
    auditoria_icms_st_status = db.Column(db.String(20), default='pendente')
    auditoria_ipi_status = db.Column(db.String(20), default='pendente')
    auditoria_pis_status = db.Column(db.String(20), default='pendente')
    auditoria_cofins_status = db.Column(db.String(20), default='pendente')
    auditoria_difal_status = db.Column(db.String(20), default='pendente')

    auditoria_icms_data = db.Column(db.DateTime)
    auditoria_icms_st_data = db.Column(db.DateTime)
    auditoria_ipi_data = db.Column(db.DateTime)
    auditoria_pis_data = db.Column(db.DateTime)
    auditoria_cofins_data = db.Column(db.DateTime)
    auditoria_difal_data = db.Column(db.DateTime)

    data_cadastro = db.Column(db.DateTime, server_default=func.now())

    def __repr__(self):
        return f"<Tributo {self.id} - Produto {self.produto_id} - Cliente {self.cliente_id}>"

    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'cliente_id': self.cliente_id,
            'produto_id': self.produto_id,
            'nota_fiscal_item_id': self.nota_fiscal_item_id,
            'data_emissao': self.data_emissao.isoformat() if self.data_emissao else None,
            'data_saida': self.data_saida.isoformat() if self.data_saida else None,

            # ICMS
            'icms_origem': self.icms_origem,
            'icms_cst': self.icms_cst,
            'icms_mod_bc': self.icms_mod_bc,
            'icms_p_red_bc': float(self.icms_p_red_bc) if self.icms_p_red_bc else None,
            'icms_vbc': float(self.icms_vbc) if self.icms_vbc else None,
            'icms_aliquota': float(self.icms_aliquota) if self.icms_aliquota else None,
            'icms_valor': float(self.icms_valor) if self.icms_valor else None,
            'icms_v_op': float(self.icms_v_op) if self.icms_v_op else None,
            'icms_p_dif': float(self.icms_p_dif) if self.icms_p_dif else None,
            'icms_v_dif': float(self.icms_v_dif) if self.icms_v_dif else None,

            # ICMS-ST
            'icms_st_mod_bc': self.icms_st_mod_bc,
            'icms_st_p_mva': float(self.icms_st_p_mva) if self.icms_st_p_mva else None,
            'icms_st_vbc': float(self.icms_st_vbc) if self.icms_st_vbc else None,
            'icms_st_aliquota': float(self.icms_st_aliquota) if self.icms_st_aliquota else None,
            'icms_st_valor': float(self.icms_st_valor) if self.icms_st_valor else None,

            # IPI
            'ipi_cst': self.ipi_cst,
            'ipi_vbc': float(self.ipi_vbc) if self.ipi_vbc else None,
            'ipi_aliquota': float(self.ipi_aliquota) if self.ipi_aliquota else None,
            'ipi_valor': float(self.ipi_valor) if self.ipi_valor else None,
            'ipi_codigo_enquadramento': self.ipi_codigo_enquadramento,
            'ipi_ex': self.ipi_ex,

            # PIS
            'pis_cst': self.pis_cst,
            'pis_vbc': float(self.pis_vbc) if self.pis_vbc else None,
            'pis_aliquota': float(self.pis_aliquota) if self.pis_aliquota else None,
            'pis_valor': float(self.pis_valor) if self.pis_valor else None,
            'pis_p_red_bc': float(self.pis_p_red_bc) if self.pis_p_red_bc else None,

            # COFINS
            'cofins_cst': self.cofins_cst,
            'cofins_vbc': float(self.cofins_vbc) if self.cofins_vbc else None,
            'cofins_aliquota': float(self.cofins_aliquota) if self.cofins_aliquota else None,
            'cofins_valor': float(self.cofins_valor) if self.cofins_valor else None,
            'cofins_p_red_bc': float(self.cofins_p_red_bc) if self.cofins_p_red_bc else None,

            # DIFAL
            'difal_vbc': float(self.difal_vbc) if self.difal_vbc else None,
            'difal_p_fcp_uf_dest': float(self.difal_p_fcp_uf_dest) if self.difal_p_fcp_uf_dest else None,
            'difal_p_icms_uf_dest': float(self.difal_p_icms_uf_dest) if self.difal_p_icms_uf_dest else None,
            'difal_p_icms_inter': float(self.difal_p_icms_inter) if self.difal_p_icms_inter else None,
            'difal_p_icms_inter_part': float(self.difal_p_icms_inter_part) if self.difal_p_icms_inter_part else None,
            'difal_v_fcp_uf_dest': float(self.difal_v_fcp_uf_dest) if self.difal_v_fcp_uf_dest else None,
            'difal_v_icms_uf_dest': float(self.difal_v_icms_uf_dest) if self.difal_v_icms_uf_dest else None,
            'difal_v_icms_uf_remet': float(self.difal_v_icms_uf_remet) if self.difal_v_icms_uf_remet else None,

            # Informações adicionais
            'numero_nf': self.numero_nf,
            'chave_nf': self.chave_nf,
            'tipo_operacao': self.tipo_operacao,
            'status': self.status,

            # Valores do produto
            'quantidade': float(self.quantidade) if self.quantidade else None,
            'valor_unitario': float(self.valor_unitario) if self.valor_unitario else None,
            'valor_total': float(self.valor_total) if self.valor_total else None,
            'valor_frete': float(self.valor_frete) if self.valor_frete else None,

            'data_cadastro': self.data_cadastro.isoformat() if self.data_cadastro else None,

            # Valores de auditoria
            'cenario_icms_vbc': float(self.cenario_icms_vbc) if self.cenario_icms_vbc else None,
            'cenario_icms_valor': float(self.cenario_icms_valor) if self.cenario_icms_valor else None,
            'cenario_icms_st_vbc': float(self.cenario_icms_st_vbc) if self.cenario_icms_st_vbc else None,
            'cenario_icms_st_valor': float(self.cenario_icms_st_valor) if self.cenario_icms_st_valor else None,
            'cenario_ipi_vbc': float(self.cenario_ipi_vbc) if self.cenario_ipi_vbc else None,
            'cenario_ipi_valor': float(self.cenario_ipi_valor) if self.cenario_ipi_valor else None,
            'cenario_pis_vbc': float(self.cenario_pis_vbc) if self.cenario_pis_vbc else None,
            'cenario_pis_valor': float(self.cenario_pis_valor) if self.cenario_pis_valor else None,
            'cenario_cofins_vbc': float(self.cenario_cofins_vbc) if self.cenario_cofins_vbc else None,
            'cenario_cofins_valor': float(self.cenario_cofins_valor) if self.cenario_cofins_valor else None,
            'cenario_difal_valor': float(self.cenario_difal_valor) if self.cenario_difal_valor else None,

            # IDs dos cenários utilizados na auditoria
            'cenario_icms_id': self.cenario_icms_id,
            'cenario_icms_st_id': self.cenario_icms_st_id,
            'cenario_ipi_id': self.cenario_ipi_id,
            'cenario_pis_id': self.cenario_pis_id,
            'cenario_cofins_id': self.cenario_cofins_id,
            'cenario_difal_id': self.cenario_difal_id,

            # Status e data da auditoria geral (para compatibilidade)
            'auditoria_status': self.auditoria_status,
            'auditoria_data': self.auditoria_data.isoformat() if self.auditoria_data else None,

            # Status e data da auditoria específicos por tipo de tributo
            'auditoria_icms_status': self.auditoria_icms_status,
            'auditoria_icms_st_status': self.auditoria_icms_st_status,
            'auditoria_ipi_status': self.auditoria_ipi_status,
            'auditoria_pis_status': self.auditoria_pis_status,
            'auditoria_cofins_status': self.auditoria_cofins_status,
            'auditoria_difal_status': self.auditoria_difal_status,

            'auditoria_icms_data': self.auditoria_icms_data.isoformat() if self.auditoria_icms_data else None,
            'auditoria_icms_st_data': self.auditoria_icms_st_data.isoformat() if self.auditoria_icms_st_data else None,
            'auditoria_ipi_data': self.auditoria_ipi_data.isoformat() if self.auditoria_ipi_data else None,
            'auditoria_pis_data': self.auditoria_pis_data.isoformat() if self.auditoria_pis_data else None,
            'auditoria_cofins_data': self.auditoria_cofins_data.isoformat() if self.auditoria_cofins_data else None,
            'auditoria_difal_data': self.auditoria_difal_data.isoformat() if self.auditoria_difal_data else None
        }
