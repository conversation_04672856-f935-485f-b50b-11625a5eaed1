-- Esquema do banco de dados para o sistema de Auditoria Fiscal

-- Tabela de Escritórios
CREATE TABLE IF NOT EXISTS escritorio (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    cnpj VARCHAR(18) NOT NULL UNIQUE,
    endereco TEXT
);

-- Tabela de Empresas
CREATE TABLE IF NOT EXISTS empresa (
    id SERIAL PRIMARY KEY,
    escritorio_id INTEGER REFERENCES escritorio(id),
    nome VARCHAR(255) NOT NULL,
    cnpj VARCHAR(18) NOT NULL UNIQUE,
    data_cadastro TIMESTAMP DEFAULT NOW(),
    inscricao_estadual VARCHAR(20)
);

-- Tabela de Usuários
CREATE TABLE IF NOT EXISTS usuario (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    senha_hash VARCHAR(255) NOT NULL,
    empresas_permitidas JSONB,
    escritorio_id INTEGER REFERENCES escritorio(id),
    is_admin BOOLEAN DEFAULT FALSE, -- Controle de permissões gerais
    tipo_usuario VARCHAR(20) DEFAULT 'usuario' -- 'admin', 'escritorio', 'usuario'
);

-- Tabela para logs de atividades
CREATE TABLE IF NOT EXISTS log_atividade (
    id SERIAL PRIMARY KEY,
    usuario_id INTEGER REFERENCES usuario(id),
    acao VARCHAR(255) NOT NULL,
    descricao TEXT,
    data_hora TIMESTAMP DEFAULT NOW()
);
