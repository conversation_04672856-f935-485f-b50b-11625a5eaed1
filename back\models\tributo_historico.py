from .escritorio import db
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import JSONB

class TributoHistorico(db.Model):
    __tablename__ = 'tributo_historico'
    id = db.Column(db.Integer, primary_key=True)
    tributo_id = db.<PERSON>umn(db.<PERSON>, db.<PERSON><PERSON>('tributo.id'), nullable=False)
    usuario_id = db.Column(db.Integer, db.<PERSON>ey('usuario.id'), nullable=False)
    tipo_tributo = db.Column(db.String(20), nullable=False)  # 'icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal'
    status_anterior = db.Column(db.String(20))
    status_novo = db.Column(db.String(20))
    valores_anteriores = db.Column(JSONB)  # Armazena os valores anteriores em formato JSON
    valores_novos = db.Column(JSONB)  # Armazena os novos valores em formato JSON
    data_alteracao = db.Column(db.DateTime, server_default=func.now())

    # Relacionamentos
    tributo = db.relationship('Tributo', backref='historicos', lazy=True)
    usuario = db.relationship('Usuario', backref='alteracoes_tributos', lazy=True)

    def __repr__(self):
        return f"<TributoHistorico {self.id} - Tributo {self.tributo_id} - Tipo {self.tipo_tributo}>"

    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'tributo_id': self.tributo_id,
            'usuario_id': self.usuario_id,
            'tipo_tributo': self.tipo_tributo,
            'status_anterior': self.status_anterior,
            'status_novo': self.status_novo,
            'valores_anteriores': self.valores_anteriores,
            'valores_novos': self.valores_novos,
            'data_alteracao': self.data_alteracao.isoformat() if self.data_alteracao else None,
            'usuario': self.usuario.nome if self.usuario else None
        }
