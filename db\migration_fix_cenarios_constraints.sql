-- Migration to fix constraints on cenario tables to allow multiple production scenarios
-- This migration removes the specific constraint that is causing the issue

-- Identificar e remover a restrição existente em cenario_icms
DO $$
DECLARE
    constraint_name text;
BEGIN
    SELECT conname INTO constraint_name
    FROM pg_constraint
    WHERE conrelid = 'cenario_icms'::regclass
    AND contype = 'u'
    AND conname = 'cenario_icms_empresa_id_cliente_id_produto_id_cfop_status_key';

    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE cenario_icms DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Constraint % dropped from cenario_icms', constraint_name;
    ELSE
        RAISE NOTICE 'Constraint cenario_icms_empresa_id_cliente_id_produto_id_cfop_status_key not found';
    END IF;
END $$;

-- Identificar e remover a restrição existente em cenario_icms_st
DO $$
DECLARE
    constraint_name text;
BEGIN
    SELECT conname INTO constraint_name
    FROM pg_constraint
    WHERE conrelid = 'cenario_icms_st'::regclass
    AND contype = 'u'
    AND conname LIKE '%empresa_id_cliente_id_produto_id_cfop_status%';

    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE cenario_icms_st DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Constraint % dropped from cenario_icms_st', constraint_name;
    ELSE
        RAISE NOTICE 'Similar constraint not found on cenario_icms_st';
    END IF;
END $$;

-- Identificar e remover a restrição existente em cenario_pis
DO $$
DECLARE
    constraint_name text;
BEGIN
    SELECT conname INTO constraint_name
    FROM pg_constraint
    WHERE conrelid = 'cenario_pis'::regclass
    AND contype = 'u'
    AND conname LIKE '%empresa_id_cliente_id_produto_id_cfop_status%';

    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE cenario_pis DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Constraint % dropped from cenario_pis', constraint_name;
    ELSE
        RAISE NOTICE 'Similar constraint not found on cenario_pis';
    END IF;
END $$;

-- Identificar e remover a restrição existente em cenario_cofins
DO $$
DECLARE
    constraint_name text;
BEGIN
    SELECT conname INTO constraint_name
    FROM pg_constraint
    WHERE conrelid = 'cenario_cofins'::regclass
    AND contype = 'u'
    AND conname LIKE '%empresa_id_cliente_id_produto_id_cfop_status%';

    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE cenario_cofins DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Constraint % dropped from cenario_cofins', constraint_name;
    ELSE
        RAISE NOTICE 'Similar constraint not found on cenario_cofins';
    END IF;
END $$;

-- Identificar e remover a restrição existente em cenario_difal
DO $$
DECLARE
    constraint_name text;
BEGIN
    SELECT conname INTO constraint_name
    FROM pg_constraint
    WHERE conrelid = 'cenario_difal'::regclass
    AND contype = 'u'
    AND conname LIKE '%empresa_id_cliente_id_produto_id_cfop_status%';

    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE cenario_difal DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Constraint % dropped from cenario_difal', constraint_name;
    ELSE
        RAISE NOTICE 'Similar constraint not found on cenario_difal';
    END IF;
END $$;

-- Adicionar novas restrições que permitam múltiplos cenários em produção
ALTER TABLE cenario_icms ADD CONSTRAINT cenario_icms_unique_except_producao
    EXCLUDE USING btree (empresa_id WITH =, cliente_id WITH =, produto_id WITH =, cfop WITH =, status WITH =)
    WHERE (status != 'producao');

ALTER TABLE cenario_icms_st ADD CONSTRAINT cenario_icms_st_unique_except_producao
    EXCLUDE USING btree (empresa_id WITH =, cliente_id WITH =, produto_id WITH =, cfop WITH =, status WITH =)
    WHERE (status != 'producao');

ALTER TABLE cenario_pis ADD CONSTRAINT cenario_pis_unique_except_producao
    EXCLUDE USING btree (empresa_id WITH =, cliente_id WITH =, produto_id WITH =, cfop WITH =, status WITH =)
    WHERE (status != 'producao');

ALTER TABLE cenario_cofins ADD CONSTRAINT cenario_cofins_unique_except_producao
    EXCLUDE USING btree (empresa_id WITH =, cliente_id WITH =, produto_id WITH =, cfop WITH =, status WITH =)
    WHERE (status != 'producao');

ALTER TABLE cenario_difal ADD CONSTRAINT cenario_difal_unique_except_producao
    EXCLUDE USING btree (empresa_id WITH =, cliente_id WITH =, produto_id WITH =, cfop WITH =, status WITH =)
    WHERE (status != 'producao');

-- Adicionar comentários para documentar as alterações
COMMENT ON CONSTRAINT cenario_icms_unique_except_producao ON cenario_icms IS 'Restrição de unicidade que permite múltiplos cenários em produção';
COMMENT ON CONSTRAINT cenario_icms_st_unique_except_producao ON cenario_icms_st IS 'Restrição de unicidade que permite múltiplos cenários em produção';
COMMENT ON CONSTRAINT cenario_pis_unique_except_producao ON cenario_pis IS 'Restrição de unicidade que permite múltiplos cenários em produção';
COMMENT ON CONSTRAINT cenario_cofins_unique_except_producao ON cenario_cofins IS 'Restrição de unicidade que permite múltiplos cenários em produção';
COMMENT ON CONSTRAINT cenario_difal_unique_except_producao ON cenario_difal IS 'Restrição de unicidade que permite múltiplos cenários em produção';
