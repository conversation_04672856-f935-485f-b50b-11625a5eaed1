#!/usr/bin/env python3
"""
Script para testar se a auditoria está funcionando com cenários inconsistentes
"""

import sys
import os
sys.path.append('back')

from models import db, Tributo, CenarioICMS, CenarioICMSST, CenarioIPI, CenarioPIS, CenarioCOFINS, CenarioDIFAL
from services.auditoria_service import AuditoriaService

def test_cenarios_inconsistentes():
    """Testa se cenários inconsistentes são encontrados"""
    
    # IDs dos produtos mencionados
    produto_ids = [4104002, 710001]  # TAMBOR USADO e BORRA
    
    print("🔍 Verificando cenários inconsistentes...")
    
    for produto_id in produto_ids:
        print(f"\n📦 Produto ID: {produto_id}")
        
        # Verificar cenários ICMS inconsistentes
        cenarios_icms = CenarioICMS.query.filter(
            CenarioICMS.produto_id == produto_id,
            CenarioICMS.status.like('incons%')
        ).all()
        
        print(f"  ICMS: {len(cenarios_icms)} cenários inconsistentes")
        for c in cenarios_icms:
            print(f"    - ID: {c.id}, Status: {c.status}, CFOP: {c.cfop}, Cliente: {c.cliente_id}")
        
        # Verificar cenários IPI inconsistentes
        cenarios_ipi = CenarioIPI.query.filter(
            CenarioIPI.produto_id == produto_id,
            CenarioIPI.status.like('incons%')
        ).all()
        
        print(f"  IPI: {len(cenarios_ipi)} cenários inconsistentes")
        for c in cenarios_ipi:
            print(f"    - ID: {c.id}, Status: {c.status}, CFOP: {c.cfop}, Cliente: {c.cliente_id}")
        
        # Verificar cenários PIS inconsistentes
        cenarios_pis = CenarioPIS.query.filter(
            CenarioPIS.produto_id == produto_id,
            CenarioPIS.status.like('incons%')
        ).all()
        
        print(f"  PIS: {len(cenarios_pis)} cenários inconsistentes")
        for c in cenarios_pis:
            print(f"    - ID: {c.id}, Status: {c.status}, CFOP: {c.cfop}, Cliente: {c.cliente_id}")
        
        # Verificar cenários COFINS inconsistentes
        cenarios_cofins = CenarioCOFINS.query.filter(
            CenarioCOFINS.produto_id == produto_id,
            CenarioCOFINS.status.like('incons%')
        ).all()
        
        print(f"  COFINS: {len(cenarios_cofins)} cenários inconsistentes")
        for c in cenarios_cofins:
            print(f"    - ID: {c.id}, Status: {c.status}, CFOP: {c.cfop}, Cliente: {c.cliente_id}")

def test_tributos_relacionados():
    """Testa se há tributos relacionados aos produtos"""
    
    produto_ids = [4104002, 710001]
    
    print("\n🧾 Verificando tributos relacionados...")
    
    for produto_id in produto_ids:
        tributos = Tributo.query.filter_by(produto_id=produto_id).all()
        print(f"\n📦 Produto ID {produto_id}: {len(tributos)} tributos encontrados")
        
        for i, t in enumerate(tributos[:3]):  # Limitar a 3 para não sobrecarregar
            print(f"  Tributo {i+1}: ID={t.id}, Cliente={t.cliente_id}, Data={t.data_emissao}, Tipo_Op={t.tipo_operacao}")
            print(f"    Status Auditoria: ICMS={getattr(t, 'auditoria_icms_status', 'N/A')}, IPI={getattr(t, 'auditoria_ipi_status', 'N/A')}")

def test_auditoria_service():
    """Testa o serviço de auditoria com os produtos específicos"""
    
    print("\n🔧 Testando serviço de auditoria...")
    
    # Usar empresa_id 1 (ajustar conforme necessário)
    empresa_id = 1
    usuario_id = 1
    
    try:
        auditoria_service = AuditoriaService(empresa_id, usuario_id)
        
        # Testar busca de cenário inconsistente diretamente
        produto_ids = [4104002, 710001]
        
        for produto_id in produto_ids:
            tributos = Tributo.query.filter_by(
                empresa_id=empresa_id,
                produto_id=produto_id
            ).limit(1).all()
            
            if tributos:
                tributo = tributos[0]
                print(f"\n🧪 Testando com tributo ID {tributo.id} (produto {produto_id})")
                
                # Testar busca de cenário ICMS
                cenario, status = auditoria_service._buscar_cenario_vigente('icms', tributo)
                if cenario:
                    print(f"  ✅ Cenário ICMS encontrado: ID={cenario.id}, Status={cenario.status}, Tipo={status}")
                else:
                    print(f"  ❌ Nenhum cenário ICMS encontrado")
                
                # Testar busca de cenário IPI
                cenario, status = auditoria_service._buscar_cenario_vigente('ipi', tributo)
                if cenario:
                    print(f"  ✅ Cenário IPI encontrado: ID={cenario.id}, Status={cenario.status}, Tipo={status}")
                else:
                    print(f"  ❌ Nenhum cenário IPI encontrado")
            else:
                print(f"  ⚠️  Nenhum tributo encontrado para produto {produto_id}")
                
    except Exception as e:
        print(f"❌ Erro no teste do serviço: {e}")

if __name__ == '__main__':
    from app import create_app
    app = create_app()
    
    with app.app_context():
        print("🚀 Iniciando testes de cenários inconsistentes...\n")
        
        test_cenarios_inconsistentes()
        test_tributos_relacionados()
        test_auditoria_service()
        
        print("\n✅ Testes concluídos!")
