-- Script final para corrigir as restrições de unicidade nas tabelas de cenários

-- Remover todas as restrições de unicidade existentes nas tabelas de cenários
DO $$
DECLARE
    constraints RECORD;
BEGIN
    -- Listar todas as restrições de unicidade nas tabelas de cenários
    FOR constraints IN
        SELECT conname, conrelid::regclass AS tablename
        FROM pg_constraint
        WHERE conrelid::regclass::text LIKE 'cenario_%'
        AND contype = 'u'
    LOOP
        EXECUTE 'ALTER TABLE ' || constraints.tablename || ' DROP CONSTRAINT ' || constraints.conname;
        RAISE NOTICE 'Dropped constraint % from table %', constraints.conname, constraints.tablename;
    END LOOP;
END $$;

-- Remover todas as restrições de exclusão existentes nas tabelas de cenários
DO $$
DECLARE
    constraints RECORD;
BEGIN
    -- Listar todas as restrições de exclusão nas tabelas de cenários
    FOR constraints IN
        SELECT conname, conrelid::regclass AS tablename
        FROM pg_constraint
        WHERE conrelid::regclass::text LIKE 'cenario_%'
        AND contype = 'x'
    LOOP
        EXECUTE 'ALTER TABLE ' || constraints.tablename || ' DROP CONSTRAINT ' || constraints.conname;
        RAISE NOTICE 'Dropped constraint % from table %', constraints.conname, constraints.tablename;
    END LOOP;
END $$;

-- Adicionar novas restrições que permitam múltiplos cenários em produção
-- Estas restrições só se aplicam a cenários com status 'novo' ou 'inconsistente'

-- Para cenario_icms
ALTER TABLE cenario_icms ADD CONSTRAINT cenario_icms_novo_inconsistente_unique
    UNIQUE (empresa_id, cliente_id, produto_id, cfop, status)
    WHERE (status IN ('novo', 'inconsistente'));

-- Para cenario_icms_st
ALTER TABLE cenario_icms_st ADD CONSTRAINT cenario_icms_st_novo_inconsistente_unique
    UNIQUE (empresa_id, cliente_id, produto_id, cfop, status)
    WHERE (status IN ('novo', 'inconsistente'));

-- Para cenario_ipi
ALTER TABLE cenario_ipi ADD CONSTRAINT cenario_ipi_novo_inconsistente_unique
    UNIQUE (empresa_id, cliente_id, produto_id, cfop, status)
    WHERE (status IN ('novo', 'inconsistente'));

-- Para cenario_pis
ALTER TABLE cenario_pis ADD CONSTRAINT cenario_pis_novo_inconsistente_unique
    UNIQUE (empresa_id, cliente_id, produto_id, cfop, status)
    WHERE (status IN ('novo', 'inconsistente'));

-- Para cenario_cofins
ALTER TABLE cenario_cofins ADD CONSTRAINT cenario_cofins_novo_inconsistente_unique
    UNIQUE (empresa_id, cliente_id, produto_id, cfop, status)
    WHERE (status IN ('novo', 'inconsistente'));

-- Para cenario_difal
ALTER TABLE cenario_difal ADD CONSTRAINT cenario_difal_novo_inconsistente_unique
    UNIQUE (empresa_id, cliente_id, produto_id, cfop, status)
    WHERE (status IN ('novo', 'inconsistente'));
