# Sistema de Auditoria Fiscal

## Visão Geral
O Sistema de Auditoria Fiscal é uma solução abrangente e robusta para gestão contábil e fiscal, desenvolvida para atender às necessidades de escritórios de contabilidade e departamentos fiscais de empresas de todos os portes. A plataforma oferece um ecossistema completo para gerenciamento fiscal, desde a captura de documentos até a geração de relatórios gerenciais detalhados.

## 🚀 Funcionalidades Principais

### 1. Gestão de Contas e Acessos
- **Múltiplos Escritórios**: Suporte a diversos escritórios contábeis na mesma instalação
- **Hierarquia de Usuários**: Diferentes níveis de acesso (Administrador, Contador, Auxiliar)
- **Empresas Clientes**: Cadastro completo de empresas clientes com todos os dados fiscais
- **Segurança Avançada**: Autenticação JWT com controle de sessão e políticas de senha fortes

### 2. Integração com Receita Federal
- **Consulta CNPJ Automática**: Busca automática de dados cadastrais na Receita Federal
- **Classificação Automática**: Identificação automática do regime tributário (Simples Nacional, Lucro Presumido, Lucro Real)
- **Mapeamento CNAE**: Sistema inteligente que associa o CNAE à atividade econômica correspondente
- **Atualização de Dados**: Sincronização periódica para manter os dados sempre atualizados

### 3. Cadastro de Produtos e Serviços
- **Cadastro Completo**: Inclusão de produtos com NCM, CEST, códigos de barras e descrições detalhadas
- **Tributação Específica**: Definição de regras tributárias específicas por produto
- **Categorização**: Organização por categorias e subcategorias para fácil localização
- **Importação em Lote**: Carga inicial de produtos via planilha

### 4. Gestão de Clientes Avançada
- **Cadastro Completo**: Dados cadastrais, fiscais e de contato
- **Histórico de Operações**: Acompanhamento de todas as interações e documentos
- **Classificação Automática**: Definição automática de características fiscais baseadas no CNAE
- **Múltiplos Contatos**: Cadastro de vários contatos por cliente

### 5. Importação de Documentos Fiscais
- **Upload em Lote**: Importação múltipla de arquivos XML de NF-e
- **Processamento Assíncrono**: Processamento em segundo plano para não travar a interface
- **Validação Completa**: Verificação de estrutura, assinatura digital e consistência dos dados
- **Tratamento de Erros**: Identificação clara de problemas e sugestões de correção
- **Notas Fiscais Físicas**: Cadastro manual para documentos não eletrônicos

### 6. Auditoria Fiscal Inteligente
- **Verificação Automática**: Análise completa dos impostos (ICMS, ICMS-ST, IPI, PIS, COFINS, DIFAL)
- **Comparação de Valores**: Confronto entre valores declarados e calculados
- **Classificação Automática**: Identificação de inconformidades e classificação por gravidade
- **Cenários de Auditoria**: Geração automática de cenários (novos, inconsistentes, aprovados)
- **Filtros Avançados**: Busca por período, tipo de operação, cliente, produto, etc.

### 7. Gestão de Cenários Fiscais
- **Criação de Cenários**: Definição de regras tributárias específicas
- **Simulação de Impacto**: Análise de como diferentes cenários afetam os valores tributários
- **Aprovação de Cenários**: Fluxo de trabalho para validação de cenários
- **Histórico de Alterações**: Rastreabilidade completa de mudanças

### 8. Relatórios e Dashboard
- **Visão Geral**: Painel com indicadores-chave de desempenho (KPIs)
- **Relatórios Personalizáveis**: Criação de relatórios sob demanda
- **Exportação**: Geração de arquivos em Excel, PDF e CSV
- **Gráficos Interativos**: Visualização de dados para análise rápida
- **Comparativo Mensal**: Análise de variação de tributos ao longo do tempo

## 🏗️ Arquitetura do Sistema

### Backend
- **Arquitetura RESTful** com separação clara de responsabilidades
- **Padrão MVC** (Model-View-Controller) para organização do código
- **Processamento Assíncrono** com Celery para tarefas pesadas
- **Cache** para melhor desempenho em consultas frequentes
- **Filas de Processamento** para gerenciamento de tarefas em segundo plano

### Frontend
- **Interface Responsiva** que se adapta a diferentes dispositivos
- **SPA (Single Page Application)** para melhor experiência do usuário
- **Componentes Reutilizáveis** para manter consistência visual
- **Validação em Tempo Real** para melhor usabilidade

## 🛠️ Tecnologias Utilizadas

### Backend
- **Python 3.11**
- **Flask** - Framework web
- **SQLAlchemy** - ORM para banco de dados
- **PostgreSQL** - Banco de dados relacional
- **JWT** - Autenticação e autorização
- **Celery** - Processamento assíncrono
- **lxml** - Processamento de XML

### Frontend
- **HTML5, CSS3, JavaScript**
- **jQuery** - Manipulação do DOM
- **DataTables** - Exibição de tabelas
- **Bootstrap** - Estilização responsiva
- **Chart.js** - Gráficos e visualizações

### Ferramentas de Desenvolvimento
- **Git** - Controle de versão
- **Docker** - Containerização
- **Poetry** - Gerenciamento de dependências
- **Black** - Formatação de código
- **Pylint** - Análise estática

## 📊 Estrutura do Projeto

```
back/
├── models/           # Modelos de banco de dados
├── routes/           # Rotas da API
├── services/         # Lógica de negócios
├── utils/            # Utilitários e helpers
└── app.py            # Aplicação principal

front/
├── static/         # Arquivos estáticos (JS, CSS, imagens)
└── templates/       # Templates HTML
```

## 🔍 Destaques Técnicos

### Processamento de XML
- Análise eficiente de grandes volumes de NF-e
- Extração e normalização de dados fiscais
- Tratamento de diferentes versões de esquemas XML

### Auditoria Automatizada
- Regras de negócio configuráveis
- Cálculos tributários precisos
- Identificação de inconformidades
- Sugestões de correção

### Segurança
- Autenticação JWT
- Controle de acesso baseado em perfis
- Criptografia de dados sensíveis
- Logs detalhados de atividades

## 🏆 Benefícios do Sistema

### Para Escritórios Contábeis
- **Eficiência Operacional**: Redução de até 70% no tempo gasto com auditorias manuais
- **Precisão nos Cálculos**: Eliminação de erros humanos nos cálculos tributários
- **Escalabilidade**: Capacidade de atender mais clientes sem aumentar a equipe
- **Diferencial Competitivo**: Oferecer auditorias fiscais detalhadas como serviço adicional
- **Redução de Riscos**: Identificação proativa de problemas fiscais antes de se tornarem multas
- **Padronização**: Processos uniformes para todos os clientes

### Para Empresas Clientes
- **Conformidade Fiscal**: Garantia de adequação à legislação tributária
- **Economia Financeira**: Identificação de oportunidades de recuperação de créditos
- **Visibilidade**: Acesso a relatórios detalhados sobre a situação fiscal
- **Previsibilidade**: Melhor planejamento tributário com cenários simulados
- **Segurança**: Dados fiscais armazenados com criptografia e backup
- **Integração**: Compatibilidade com sistemas contábeis e ERP existentes

## 🚀 Guia de Instalação e Execução

### Pré-requisitos
- **Sistema Operacional**: Windows 10/11, macOS 10.15+, ou Linux
- **Python**: 3.11 ou superior
- **Banco de Dados**: PostgreSQL 13+ com extensão PostGIS
- **Cache/Message Broker**: Redis 6+
- **Node.js**: 16+ (para compilação de assets frontend)
- **Espaço em Disco**: Mínimo de 2GB (dependendo do volume de documentos)
- **Memória RAM**: Mínimo 4GB (8GB recomendado para produção)

### Instalação Passo a Passo

1. **Clonar o Repositório**
   ```bash
   git clone https://github.com/seu-usuario/auditoria-fiscal.git
   cd auditoria-fiscal
   ```

2. **Configurar Ambiente Virtual**
   ```bash
   # Linux/macOS
   python3 -m venv venv
   source venv/bin/activate
   
   # Windows
   python -m venv venv
   .\venv\Scripts\activate
   ```

3. **Instalar Dependências**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

4. **Configuração do Ambiente**
   ```bash
   cp .env.example .env
   # Editar o arquivo .env com suas configurações
   ```
   
   Principais configurações a serem ajustadas:
   - `DATABASE_URL`: URL de conexão com o PostgreSQL
   - `SECRET_KEY`: Chave secreta para criptografia
   - `JWT_SECRET_KEY`: Chave para tokens JWT
   - `REDIS_URL`: URL do servidor Redis
   - `RECEITA_WS_TOKEN`: Token para API da Receita WS

5. **Configurar Banco de Dados**
   ```bash
   # Criar banco de dados (execute no psql)
   createdb auditoria_fiscal
   
   # Aplicar migrações
   flask db upgrade
   
   # Carregar dados iniciais (opcional)
   flask load-data initial
   ```

6. **Configurar Frontend**
   ```bash
   cd front
   npm install
   npm run build
   cd ..
   ```

7. **Iniciar Serviços**
   Em terminais separados:
   
   ```bash
   # Terminal 1: Servidor Redis
   redis-server
   
   # Terminal 2: Worker Celery
   celery -A app.celery worker --loglevel=info
   
   # Terminal 3: Aplicação Flask
   flask run
   ```

8. **Acessar a Aplicação**
   Abra o navegador e acesse:
   ```
   http://localhost:5000
   ```
   
   Credenciais iniciais (altere no primeiro acesso):
   - Email: <EMAIL>
   - Senha: admin123

### Docker (Opção Alternativa)

Para facilitar a implantação, você pode usar o Docker Compose:

```bash
# Construir e iniciar os containers
docker-compose up --build

# Executar migrações
docker-compose exec web flask db upgrade

# Carregar dados iniciais
docker-compose exec web flask load-data initial
```

## 📝 Licença
Este projeto está sob a licença MIT. Consulte o arquivo LICENSE para obter mais detalhes.

## 👥 Contribuição
Contribuições são bem-vindas! Sinta-se à vontade para abrir issues e enviar pull requests.

## 📞 Contato
Para mais informações, entre em contato através do email: [<EMAIL>]
