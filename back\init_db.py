"""
Script para inicializar o banco de dados e criar um usuário administrador
"""

import os
import bcrypt
from dotenv import load_dotenv
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Carregar variáveis de ambiente
load_dotenv()

# Configurações do banco de dados
db_url = os.getenv('DATABASE_URL', 'postgresql://postgres:postgres@localhost/auditoria_fiscal')

# Extrair componentes da URL do banco de dados
db_parts = db_url.replace('postgresql://', '').split('/')
db_conn_parts = db_parts[0].split('@')
db_user_pass = db_conn_parts[0].split(':')
db_host_port = db_conn_parts[1].split(':')

db_user = db_user_pass[0]
db_pass = db_user_pass[1] if len(db_user_pass) > 1 else ''
db_host = db_host_port[0]
db_port = db_host_port[1] if len(db_host_port) > 1 else '5432'
db_name = db_parts[1]

def criar_banco_dados():
    """Cria o banco de dados se não existir"""
    try:
        # Conectar ao PostgreSQL sem especificar um banco de dados
        conn = psycopg2.connect(
            user=db_user,
            password=db_pass,
            host=db_host,
            port=db_port
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Verificar se o banco de dados já existe
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (db_name,))
        exists = cursor.fetchone()
        
        if not exists:
            print(f"Criando banco de dados '{db_name}'...")
            cursor.execute(f"CREATE DATABASE {db_name}")
            print(f"Banco de dados '{db_name}' criado com sucesso!")
        else:
            print(f"Banco de dados '{db_name}' já existe.")
        
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        print(f"Erro ao criar banco de dados: {e}")
        return False

def executar_schema():
    """Executa o script de schema para criar as tabelas"""
    try:
        # Conectar ao banco de dados
        conn = psycopg2.connect(
            user=db_user,
            password=db_pass,
            host=db_host,
            port=db_port,
            database=db_name
        )
        cursor = conn.cursor()
        
        # Obter o caminho absoluto para o arquivo schema.sql
        import os
        script_dir = os.path.dirname(os.path.abspath(__file__))
        schema_path = os.path.join(os.path.dirname(script_dir), 'db', 'schema.sql')
        
        # Ler e executar o script de schema
        with open(schema_path, 'r') as f:
            schema_sql = f.read()
            cursor.execute(schema_sql)
        
        conn.commit()
        print("Schema executado com sucesso!")
        
        return conn, cursor
    except Exception as e:
        print(f"Erro ao executar schema: {e}")
        return None, None

def gerar_hash_senha(senha):
    """Gera um hash bcrypt para a senha"""
    return bcrypt.hashpw(senha.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def criar_usuario_admin(conn, cursor):
    """Cria um usuário administrador"""
    try:
        # Verificar se já existe um usuário admin
        cursor.execute("SELECT id FROM usuario WHERE tipo_usuario = 'admin'")
        admin_existente = cursor.fetchone()
        
        if admin_existente:
            print(f"Usuário administrador já existe (ID: {admin_existente[0]})")
            return
        
        # Dados do usuário admin
        nome = "Administrador"
        email = "<EMAIL>"
        senha = "admin123"  # Em produção, use uma senha forte!
        senha_hash = gerar_hash_senha(senha)
        
        # Inserir o usuário admin
        cursor.execute(
            """
            INSERT INTO usuario 
            (nome, email, senha_hash, is_admin, tipo_usuario, empresas_permitidas) 
            VALUES (%s, %s, %s, %s, %s, %s) 
            RETURNING id
            """,
            (nome, email, senha_hash, True, 'admin', '[]')
        )
        
        usuario_id = cursor.fetchone()[0]
        conn.commit()
        
        print(f"Usuário administrador criado com sucesso (ID: {usuario_id})")
        print(f"Email: {email}")
        print(f"Senha: {senha}")
    except Exception as e:
        conn.rollback()
        print(f"Erro ao criar usuário administrador: {e}")

def main():
    """Função principal"""
    print("Inicializando banco de dados...")
    
    # Criar banco de dados
    if not criar_banco_dados():
        return
    
    # Executar schema
    conn, cursor = executar_schema()
    if not conn or not cursor:
        return
    
    # Criar usuário administrador
    criar_usuario_admin(conn, cursor)
    
    # Fechar conexão
    cursor.close()
    conn.close()
    
    print("Inicialização concluída!")

if __name__ == "__main__":
    main()


