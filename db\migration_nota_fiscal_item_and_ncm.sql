-- Migration to implement nota_fiscal_item table and update scenario handling for CFOP and NCM

-- Create nota_fiscal_item table if it doesn't exist yet
CREATE TABLE IF NOT EXISTS nota_fiscal_item (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER NOT NULL,
    escritorio_id INTEGER NOT NULL,
    cliente_id INTEGER NOT NULL,
    produto_id INTEGER NOT NULL,
    numero_nf VARCHAR(20) NOT NULL,
    chave_nf VARCHAR(44),
    data_emissao DATE NOT NULL,
    cfop VARCHAR(10),
    ncm VARCHAR(20),
    unidade_comercial VARCHAR(10),
    quantidade DECIMAL(15, 4),
    valor_unitario DECIMAL(15, 4),
    valor_total DECIMAL(15, 2),
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (empresa_id) REFERENCES empresa(id),
    FOREI<PERSON><PERSON> KEY (escritorio_id) REFERENCES escritorio(id),
    FOREIG<PERSON> KEY (cliente_id) REFERENCES cliente(id),
    FOREIGN KEY (produto_id) REFERENCES produto(id)
);

-- Create indexes for nota_fiscal_item table
CREATE INDEX IF NOT EXISTS idx_nota_fiscal_item_empresa ON nota_fiscal_item(empresa_id);
CREATE INDEX IF NOT EXISTS idx_nota_fiscal_item_escritorio ON nota_fiscal_item(escritorio_id);
CREATE INDEX IF NOT EXISTS idx_nota_fiscal_item_cliente ON nota_fiscal_item(cliente_id);
CREATE INDEX IF NOT EXISTS idx_nota_fiscal_item_produto ON nota_fiscal_item(produto_id);
CREATE INDEX IF NOT EXISTS idx_nota_fiscal_item_chave ON nota_fiscal_item(chave_nf);
CREATE INDEX IF NOT EXISTS idx_nota_fiscal_item_data ON nota_fiscal_item(data_emissao);

-- Add NCM column to all scenario tables if they don't have it yet
ALTER TABLE cenario_icms ADD COLUMN IF NOT EXISTS ncm VARCHAR(20);
ALTER TABLE cenario_icms_st ADD COLUMN IF NOT EXISTS ncm VARCHAR(20);
ALTER TABLE cenario_ipi ADD COLUMN IF NOT EXISTS ncm VARCHAR(20);
ALTER TABLE cenario_pis ADD COLUMN IF NOT EXISTS ncm VARCHAR(20);
ALTER TABLE cenario_cofins ADD COLUMN IF NOT EXISTS ncm VARCHAR(20);
ALTER TABLE cenario_difal ADD COLUMN IF NOT EXISTS ncm VARCHAR(20);

-- Add reference to nota_fiscal_item in tributo table
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS nota_fiscal_item_id INTEGER REFERENCES nota_fiscal_item(id);
CREATE INDEX IF NOT EXISTS idx_tributo_nota_fiscal_item ON tributo(nota_fiscal_item_id);

-- Add columns to store scenario IDs for each tax type in tributo table
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_icms_id INTEGER REFERENCES cenario_icms(id);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_icms_st_id INTEGER REFERENCES cenario_icms_st(id);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_ipi_id INTEGER REFERENCES cenario_ipi(id);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_pis_id INTEGER REFERENCES cenario_pis(id);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_cofins_id INTEGER REFERENCES cenario_cofins(id);
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS cenario_difal_id INTEGER REFERENCES cenario_difal(id);

-- Create indexes for the new scenario ID columns
CREATE INDEX IF NOT EXISTS idx_tributo_cenario_icms ON tributo(cenario_icms_id);
CREATE INDEX IF NOT EXISTS idx_tributo_cenario_icms_st ON tributo(cenario_icms_st_id);
CREATE INDEX IF NOT EXISTS idx_tributo_cenario_ipi ON tributo(cenario_ipi_id);
CREATE INDEX IF NOT EXISTS idx_tributo_cenario_pis ON tributo(cenario_pis_id);
CREATE INDEX IF NOT EXISTS idx_tributo_cenario_cofins ON tributo(cenario_cofins_id);
CREATE INDEX IF NOT EXISTS idx_tributo_cenario_difal ON tributo(cenario_difal_id);

-- Create table for audit results
CREATE TABLE IF NOT EXISTS auditoria_resultado (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER NOT NULL REFERENCES empresa(id),
    escritorio_id INTEGER NOT NULL REFERENCES escritorio(id),
    tributo_id INTEGER NOT NULL REFERENCES tributo(id),
    nota_fiscal_item_id INTEGER NOT NULL REFERENCES nota_fiscal_item(id),
    tipo_tributo VARCHAR(20) NOT NULL, -- 'icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal'
    cenario_id INTEGER NOT NULL, -- ID do cenário utilizado
    valor_nota DECIMAL(15, 2), -- Valor do tributo na nota
    valor_calculado DECIMAL(15, 2), -- Valor calculado com base no cenário
    base_calculo_nota DECIMAL(15, 2), -- Base de cálculo na nota
    base_calculo_calculada DECIMAL(15, 2), -- Base de cálculo calculada
    status VARCHAR(20) NOT NULL, -- 'conforme', 'inconsistente'
    data_auditoria TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (tributo_id, tipo_tributo)
);

-- Create indexes for auditoria_resultado table
CREATE INDEX IF NOT EXISTS idx_auditoria_resultado_empresa ON auditoria_resultado(empresa_id);
CREATE INDEX IF NOT EXISTS idx_auditoria_resultado_escritorio ON auditoria_resultado(escritorio_id);
CREATE INDEX IF NOT EXISTS idx_auditoria_resultado_tributo ON auditoria_resultado(tributo_id);
CREATE INDEX IF NOT EXISTS idx_auditoria_resultado_nota_fiscal_item ON auditoria_resultado(nota_fiscal_item_id);
CREATE INDEX IF NOT EXISTS idx_auditoria_resultado_tipo_tributo ON auditoria_resultado(tipo_tributo);
CREATE INDEX IF NOT EXISTS idx_auditoria_resultado_status ON auditoria_resultado(status);

-- Create table for audit summary
CREATE TABLE IF NOT EXISTS auditoria_sumario (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER NOT NULL REFERENCES empresa(id),
    escritorio_id INTEGER NOT NULL REFERENCES escritorio(id),
    ano INTEGER NOT NULL,
    mes INTEGER NOT NULL,
    tipo_tributo VARCHAR(20) NOT NULL, -- 'icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal'
    total_notas INTEGER NOT NULL DEFAULT 0,
    total_produtos INTEGER NOT NULL DEFAULT 0,
    valor_total_notas DECIMAL(15, 2) NOT NULL DEFAULT 0,
    valor_total_produtos DECIMAL(15, 2) NOT NULL DEFAULT 0,
    valor_total_tributo DECIMAL(15, 2) NOT NULL DEFAULT 0,
    total_conforme INTEGER NOT NULL DEFAULT 0,
    total_inconsistente INTEGER NOT NULL DEFAULT 0,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (empresa_id, ano, mes, tipo_tributo)
);

-- Create indexes for auditoria_sumario table
CREATE INDEX IF NOT EXISTS idx_auditoria_sumario_empresa ON auditoria_sumario(empresa_id);
CREATE INDEX IF NOT EXISTS idx_auditoria_sumario_escritorio ON auditoria_sumario(escritorio_id);
CREATE INDEX IF NOT EXISTS idx_auditoria_sumario_ano_mes ON auditoria_sumario(ano, mes);
CREATE INDEX IF NOT EXISTS idx_auditoria_sumario_tipo_tributo ON auditoria_sumario(tipo_tributo);

-- Add comments to the new tables
COMMENT ON TABLE nota_fiscal_item IS 'Armazena informações dos itens de nota fiscal';
COMMENT ON TABLE auditoria_resultado IS 'Armazena resultados detalhados da auditoria fiscal';
COMMENT ON TABLE auditoria_sumario IS 'Armazena resumos da auditoria fiscal por mês';
