-- Migração: Ad<PERSON><PERSON>r tabela para controle manual de status de auditoria
-- Data: 2025-01-27
-- Descrição: Permite marcar tributos como "não aplicável" manualmente

-- Criar tipos ENUM para PostgreSQL
DO $$ BEGIN
    CREATE TYPE tipo_tributo_enum AS ENUM ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE status_manual_enum AS ENUM ('nao_aplicavel', 'aplicavel');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- <PERSON><PERSON>r tabela
CREATE TABLE IF NOT EXISTS auditoria_status_manual (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER NOT NULL,
    tipo_tributo tipo_tributo_enum NOT NULL,
    status status_manual_enum NOT NULL DEFAULT 'aplicavel',
    motivo TEXT,
    data_marcacao TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    usuario_id INTEGER NOT NULL,

    -- Constraint única
    CONSTRAINT unique_empresa_tributo UNIQUE (empresa_id, tipo_tributo),

    -- Chaves estrangeiras
    CONSTRAINT fk_empresa FOREIGN KEY (empresa_id) REFERENCES empresa(id) ON DELETE CASCADE,
    CONSTRAINT fk_usuario FOREIGN KEY (usuario_id) REFERENCES usuario(id) ON DELETE RESTRICT
);

-- Criar índices
CREATE INDEX IF NOT EXISTS idx_auditoria_status_empresa_id ON auditoria_status_manual(empresa_id);
CREATE INDEX IF NOT EXISTS idx_auditoria_status_tipo_tributo ON auditoria_status_manual(tipo_tributo);
CREATE INDEX IF NOT EXISTS idx_auditoria_status_usuario_id ON auditoria_status_manual(usuario_id);

-- Comentários
COMMENT ON TABLE auditoria_status_manual IS 'Controle manual de status de auditoria por tributo';
COMMENT ON COLUMN auditoria_status_manual.empresa_id IS 'ID da empresa';
COMMENT ON COLUMN auditoria_status_manual.tipo_tributo IS 'Tipo do tributo';
COMMENT ON COLUMN auditoria_status_manual.status IS 'Status manual do tributo';
COMMENT ON COLUMN auditoria_status_manual.motivo IS 'Motivo da marcação manual';
COMMENT ON COLUMN auditoria_status_manual.data_marcacao IS 'Data da marcação';
COMMENT ON COLUMN auditoria_status_manual.usuario_id IS 'Usuário que fez a marcação';
