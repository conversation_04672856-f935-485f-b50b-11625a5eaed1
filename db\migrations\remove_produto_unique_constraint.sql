-- Migration to remove the unique constraint on produto table
-- This constraint is causing issues with XML imports

-- First, identify the constraint name
DO $$
DECLARE
    constraint_name text;
BEGIN
    SELECT conname INTO constraint_name
    FROM pg_constraint
    WHERE conrelid = 'produto'::regclass
    AND contype = 'u'
    AND array_to_string(conkey, ',') = 
        (SELECT array_to_string(array_agg(attnum ORDER BY attnum), ',')
         FROM pg_attribute
         WHERE attrelid = 'produto'::regclass
         AND attname IN ('empresa_id', 'codigo'));

    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE produto DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Constraint % dropped', constraint_name;
    ELSE
        RAISE NOTICE 'No unique constraint found on empresa_id, codigo';
    END IF;
END $$;

-- Alternatively, if we know the exact constraint name
ALTER TABLE produto DROP CONSTRAINT IF EXISTS produto_empresa_id_codigo_key;

-- Log the migration
INSERT INTO log_atividade (usuario_id, acao, descricao)
VALUES (1, 'Migration', 'Removed unique constraint on produto table (empresa_id, codigo)');
