-- Add tipo_operacao column to all scenario tables
ALTER TABLE cenario_icms ADD COLUMN IF NOT EXISTS tipo_operacao VARCHAR(1);
ALTER TABLE cenario_icms_st ADD COLUMN IF NOT EXISTS tipo_operacao VARCHAR(1);
ALTER TABLE cenario_ipi ADD COLUMN IF NOT EXISTS tipo_operacao VARCHAR(1);
ALTER TABLE cenario_pis ADD COLUMN IF NOT EXISTS tipo_operacao VARCHAR(1);
ALTER TABLE cenario_cofins ADD COLUMN IF NOT EXISTS tipo_operacao VARCHAR(1);
ALTER TABLE cenario_difal ADD COLUMN IF NOT EXISTS tipo_operacao VARCHAR(1);

-- Add comments to explain the column
COMMENT ON COLUMN cenario_icms.tipo_operacao IS 'Tipo de operação: 0 = entrada, 1 = saída (valor da tag tpNF do XML)';
COMMENT ON COLUMN cenario_icms_st.tipo_operacao IS 'Tipo de operação: 0 = entrada, 1 = saída (valor da tag tpNF do XML)';
COMMENT ON COLUMN cenario_ipi.tipo_operacao IS 'Tipo de operação: 0 = entrada, 1 = saída (valor da tag tpNF do XML)';
COMMENT ON COLUMN cenario_pis.tipo_operacao IS 'Tipo de operação: 0 = entrada, 1 = saída (valor da tag tpNF do XML)';
COMMENT ON COLUMN cenario_cofins.tipo_operacao IS 'Tipo de operação: 0 = entrada, 1 = saída (valor da tag tpNF do XML)';
COMMENT ON COLUMN cenario_difal.tipo_operacao IS 'Tipo de operação: 0 = entrada, 1 = saída (valor da tag tpNF do XML)';

-- Create indexes for better performance when filtering by tipo_operacao
CREATE INDEX IF NOT EXISTS idx_cenario_icms_tipo_operacao ON cenario_icms(tipo_operacao);
CREATE INDEX IF NOT EXISTS idx_cenario_icms_st_tipo_operacao ON cenario_icms_st(tipo_operacao);
CREATE INDEX IF NOT EXISTS idx_cenario_ipi_tipo_operacao ON cenario_ipi(tipo_operacao);
CREATE INDEX IF NOT EXISTS idx_cenario_pis_tipo_operacao ON cenario_pis(tipo_operacao);
CREATE INDEX IF NOT EXISTS idx_cenario_cofins_tipo_operacao ON cenario_cofins(tipo_operacao);
CREATE INDEX IF NOT EXISTS idx_cenario_difal_tipo_operacao ON cenario_difal(tipo_operacao);

-- Create composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_cenario_icms_empresa_direcao_tipo_op ON cenario_icms(empresa_id, direcao, tipo_operacao);
CREATE INDEX IF NOT EXISTS idx_cenario_icms_st_empresa_direcao_tipo_op ON cenario_icms_st(empresa_id, direcao, tipo_operacao);
CREATE INDEX IF NOT EXISTS idx_cenario_ipi_empresa_direcao_tipo_op ON cenario_ipi(empresa_id, direcao, tipo_operacao);
CREATE INDEX IF NOT EXISTS idx_cenario_pis_empresa_direcao_tipo_op ON cenario_pis(empresa_id, direcao, tipo_operacao);
CREATE INDEX IF NOT EXISTS idx_cenario_cofins_empresa_direcao_tipo_op ON cenario_cofins(empresa_id, direcao, tipo_operacao);
CREATE INDEX IF NOT EXISTS idx_cenario_difal_empresa_direcao_tipo_op ON cenario_difal(empresa_id, direcao, tipo_operacao);

-- Update existing data based on direcao
UPDATE cenario_icms SET tipo_operacao = CASE WHEN direcao = 'entrada' THEN '0' ELSE '1' END WHERE tipo_operacao IS NULL;
UPDATE cenario_icms_st SET tipo_operacao = CASE WHEN direcao = 'entrada' THEN '0' ELSE '1' END WHERE tipo_operacao IS NULL;
UPDATE cenario_ipi SET tipo_operacao = CASE WHEN direcao = 'entrada' THEN '0' ELSE '1' END WHERE tipo_operacao IS NULL;
UPDATE cenario_pis SET tipo_operacao = CASE WHEN direcao = 'entrada' THEN '0' ELSE '1' END WHERE tipo_operacao IS NULL;
UPDATE cenario_cofins SET tipo_operacao = CASE WHEN direcao = 'entrada' THEN '0' ELSE '1' END WHERE tipo_operacao IS NULL;
UPDATE cenario_difal SET tipo_operacao = CASE WHEN direcao = 'entrada' THEN '0' ELSE '1' END WHERE tipo_operacao IS NULL;
