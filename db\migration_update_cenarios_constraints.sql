-- Migration to update constraints on cenario tables to allow multiple production scenarios
-- This migration removes the unique constraint on status and adds a new constraint that only applies to 'novo' and 'inconsistente' statuses

-- First, identify and drop the existing constraint on cenario_icms
DO $$
DECLARE
    constraint_name text;
BEGIN
    SELECT conname INTO constraint_name
    FROM pg_constraint
    WHERE conrelid = 'cenario_icms'::regclass
    AND contype = 'u'
    AND array_to_string(conkey, ',') LIKE '%status%';

    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE cenario_icms DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Constraint % dropped from cenario_icms', constraint_name;
    ELSE
        RAISE NOTICE 'No unique constraint found on cenario_icms with status';
    END IF;
END $$;

-- Add a new constraint that only applies to 'novo' and 'inconsistente' statuses
ALTER TABLE cenario_icms ADD CONSTRAINT cenario_icms_unique_except_producao
    EXCLUDE USING btree (empresa_id WITH =, cliente_id WITH =, produto_id WITH =, cfop WITH =, status WITH =)
    WHERE (status != 'producao');

-- Repeat for cenario_icms_st
DO $$
DECLARE
    constraint_name text;
BEGIN
    SELECT conname INTO constraint_name
    FROM pg_constraint
    WHERE conrelid = 'cenario_icms_st'::regclass
    AND contype = 'u'
    AND array_to_string(conkey, ',') LIKE '%status%';

    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE cenario_icms_st DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Constraint % dropped from cenario_icms_st', constraint_name;
    ELSE
        RAISE NOTICE 'No unique constraint found on cenario_icms_st with status';
    END IF;
END $$;

ALTER TABLE cenario_icms_st ADD CONSTRAINT cenario_icms_st_unique_except_producao
    EXCLUDE USING btree (empresa_id WITH =, cliente_id WITH =, produto_id WITH =, cfop WITH =, status WITH =)
    WHERE (status != 'producao');

-- Repeat for cenario_pis
DO $$
DECLARE
    constraint_name text;
BEGIN
    SELECT conname INTO constraint_name
    FROM pg_constraint
    WHERE conrelid = 'cenario_pis'::regclass
    AND contype = 'u'
    AND array_to_string(conkey, ',') LIKE '%status%';

    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE cenario_pis DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Constraint % dropped from cenario_pis', constraint_name;
    ELSE
        RAISE NOTICE 'No unique constraint found on cenario_pis with status';
    END IF;
END $$;

ALTER TABLE cenario_pis ADD CONSTRAINT cenario_pis_unique_except_producao
    EXCLUDE USING btree (empresa_id WITH =, cliente_id WITH =, produto_id WITH =, cfop WITH =, status WITH =)
    WHERE (status != 'producao');

-- Repeat for cenario_cofins
DO $$
DECLARE
    constraint_name text;
BEGIN
    SELECT conname INTO constraint_name
    FROM pg_constraint
    WHERE conrelid = 'cenario_cofins'::regclass
    AND contype = 'u'
    AND array_to_string(conkey, ',') LIKE '%status%';

    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE cenario_cofins DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Constraint % dropped from cenario_cofins', constraint_name;
    ELSE
        RAISE NOTICE 'No unique constraint found on cenario_cofins with status';
    END IF;
END $$;

ALTER TABLE cenario_cofins ADD CONSTRAINT cenario_cofins_unique_except_producao
    EXCLUDE USING btree (empresa_id WITH =, cliente_id WITH =, produto_id WITH =, cfop WITH =, status WITH =)
    WHERE (status != 'producao');

-- Repeat for cenario_difal
DO $$
DECLARE
    constraint_name text;
BEGIN
    SELECT conname INTO constraint_name
    FROM pg_constraint
    WHERE conrelid = 'cenario_difal'::regclass
    AND contype = 'u'
    AND array_to_string(conkey, ',') LIKE '%status%';

    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE cenario_difal DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Constraint % dropped from cenario_difal', constraint_name;
    ELSE
        RAISE NOTICE 'No unique constraint found on cenario_difal with status';
    END IF;
END $$;

ALTER TABLE cenario_difal ADD CONSTRAINT cenario_difal_unique_except_producao
    EXCLUDE USING btree (empresa_id WITH =, cliente_id WITH =, produto_id WITH =, cfop WITH =, status WITH =)
    WHERE (status != 'producao');

-- Add comments to document the changes
COMMENT ON CONSTRAINT cenario_icms_unique_except_producao ON cenario_icms IS 'Restrição de unicidade que permite múltiplos cenários em produção';
COMMENT ON CONSTRAINT cenario_icms_st_unique_except_producao ON cenario_icms_st IS 'Restrição de unicidade que permite múltiplos cenários em produção';
COMMENT ON CONSTRAINT cenario_pis_unique_except_producao ON cenario_pis IS 'Restrição de unicidade que permite múltiplos cenários em produção';
COMMENT ON CONSTRAINT cenario_cofins_unique_except_producao ON cenario_cofins IS 'Restrição de unicidade que permite múltiplos cenários em produção';
COMMENT ON CONSTRAINT cenario_difal_unique_except_producao ON cenario_difal IS 'Restrição de unicidade que permite múltiplos cenários em produção';
