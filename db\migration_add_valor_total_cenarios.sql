-- Migration: Add valor_total_cenarios column to auditoria_sumario table
-- This migration adds a new column to separate values from notes vs scenarios

-- Add the new column
ALTER TABLE auditoria_sumario 
ADD COLUMN IF NOT EXISTS valor_total_cenarios DECIMAL(15, 2) NOT NULL DEFAULT 0;

-- Update existing records to set valor_total_cenarios equal to valor_total_tributo
-- This maintains compatibility with existing data
UPDATE auditoria_sumario 
SET valor_total_cenarios = valor_total_tributo 
WHERE valor_total_cenarios = 0;

-- Add comment to explain the column purpose
COMMENT ON COLUMN auditoria_sumario.valor_total_cenarios IS 'Soma dos valores dos tributos calculados pelos cenários';
COMMENT ON COLUMN auditoria_sumario.valor_total_notas IS 'Soma dos valores dos tributos das notas fiscais';
COMMENT ON COLUMN auditoria_sumario.valor_total_produtos IS 'Soma dos valores dos produtos (para compatibilidade)';
COMMENT ON COLUMN auditoria_sumario.valor_total_tributo IS 'Igual ao valor_total_cenarios (para compatibilidade)';
