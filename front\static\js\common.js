/**
 * Common.js - Auditoria Fiscal
 * Funções comuns para todas as páginas
 */

// Variáveis globais
let currentUser = null;
let selectedCompany = null;
let selectedYear = new Date().getFullYear();
let darkThemeEnabled = false;

// Inicialização
document.addEventListener('DOMContentLoaded', function () {
  console.log('DOM carregado - common.js');

  // Verificar autenticação
  checkAuth();

  // Carregar preferência de tema
  loadThemePreference();

  // Configurar navegação da sidebar
  setupSidebarNavigation();

  // Configurar toggle da sidebar
  setupSidebarToggle();

  // Configurar seletor de empresa
  setupCompanySelector();

  // Configurar seletor de ano
  setupYearSelector();

  // Configurar botão de alternância de tema
  setupThemeToggle();

  // Configurar botão de logout
  setupLogoutButton();
});

/**
 * Verifica a autenticação do usuário
 */
function checkAuth() {
  const token = localStorage.getItem('token');
  if (!token) {
    console.log('Token não encontrado, redirecionando para login');
    window.location.href = '/web';
    return;
  }

  // Verificar se o token é válido
  fetch('/me', {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  })
    .then((response) => {
      if (response.status === 401) {
        console.error('Token inválido, redirecionando para login');
        localStorage.removeItem('token');
        window.location.href = '/web';
        return;
      }

      // Token válido, continuar com a inicialização
      return response.json();
    })
    .then((user) => {
      if (user) {
        // Carregar informações do usuário
        loadUserInfo(user);
      }
    })
    .catch((error) => {
      console.error('Erro ao verificar token:', error);
    });
}

/**
 * Carrega informações do usuário
 * @param {Object} user - Dados do usuário
 */
function loadUserInfo(user) {
  currentUser = user;
  localStorage.setItem('currentUser', JSON.stringify(user));

  // Atualizar nome do usuário no header
  const userNameElement = document.getElementById('user-name');
  if (userNameElement) {
    userNameElement.textContent = user.nome;
  }

  // Carregar empresas para o seletor
  if (currentUser.tipo_usuario === 'admin' || currentUser.is_admin) {
    // Administradores veem todas as empresas
    loadAllCompanies();
  } else if (currentUser.tipo_usuario === 'escritorio') {
    // Usuários do tipo escritório veem empresas do seu escritório
    loadEscritorioCompanies(currentUser.escritorio_id);
  } else if (
    currentUser.empresas_permitidas &&
    currentUser.empresas_permitidas.length > 0
  ) {
    // Usuários comuns veem apenas empresas permitidas
    loadUserCompanies(currentUser.empresas_permitidas);
  }
}

/**
 * Carrega a preferência de tema do usuário
 */
function loadThemePreference() {
  const savedTheme = localStorage.getItem('darkTheme');

  if (savedTheme === 'true') {
    darkThemeEnabled = true;
    document.body.classList.add('dark-theme');

    // Atualizar ícone do botão se ele já existir
    const themeToggleBtn = document.getElementById('theme-toggle-btn');
    if (themeToggleBtn) {
      const icon = themeToggleBtn.querySelector('i');
      icon.classList.remove('fa-moon');
      icon.classList.add('fa-sun');
    }
  }
}

/**
 * Configura o botão de alternância de tema
 */
function setupThemeToggle() {
  const themeToggleBtn = document.getElementById('theme-toggle-btn');

  if (themeToggleBtn) {
    // Remover event listeners anteriores (se possível)
    const newThemeToggleBtn = themeToggleBtn.cloneNode(true);
    themeToggleBtn.parentNode.replaceChild(newThemeToggleBtn, themeToggleBtn);

    // Atualizar ícone inicial com base no tema atual
    const icon = newThemeToggleBtn.querySelector('i');
    if (darkThemeEnabled) {
      icon.classList.remove('fa-moon');
      icon.classList.add('fa-sun');
    }

    newThemeToggleBtn.addEventListener('click', function () {
      // Alternar o tema
      darkThemeEnabled = !darkThemeEnabled;

      // Salvar preferência
      localStorage.setItem('darkTheme', darkThemeEnabled);

      // Aplicar ou remover classe do body com uma pequena animação
      if (darkThemeEnabled) {
        document.body.classList.add('dark-theme');
        // Animar o ícone
        icon.style.transform = 'rotate(360deg)';
        setTimeout(() => {
          icon.classList.remove('fa-moon');
          icon.classList.add('fa-sun');
          icon.style.transform = '';
        }, 150);
      } else {
        document.body.classList.remove('dark-theme');
        // Animar o ícone
        icon.style.transform = 'rotate(-360deg)';
        setTimeout(() => {
          icon.classList.remove('fa-sun');
          icon.classList.add('fa-moon');
          icon.style.transform = '';
        }, 150);
      }
    });
  }
}

/**
 * Configura a navegação da sidebar
 */
function setupSidebarNavigation() {
  const navItems = document.querySelectorAll('.nav-item');
  const dropdownItems = document.querySelectorAll('.nav-dropdown-item');

  // Marcar o item ativo com base na URL atual
  const currentPath = window.location.pathname;
  let currentPage = 'home'; // Padrão
  let parentPage = '';

  // Determinar a página atual com base na URL
  if (currentPath.includes('/auditoria/entrada')) {
    currentPage = 'auditoria-entrada';
    parentPage = 'auditoria';
  } else if (currentPath.includes('/auditoria/saida')) {
    currentPage = 'auditoria-saida';
    parentPage = 'auditoria';
  } else if (currentPath.includes('/cenarios/entrada')) {
    currentPage = 'cenarios-entrada';
    parentPage = 'cenarios';
  } else if (currentPath.includes('/cenarios/saida')) {
    currentPage = 'cenarios-saida';
    parentPage = 'cenarios';
  } else if (currentPath.includes('/clientes')) {
    currentPage = 'clientes';
  } else if (currentPath.includes('/produto')) {
    currentPage = 'produto';
  } else if (currentPath.includes('/importacao')) {
    currentPage = 'importacao';
  } else if (currentPath.includes('/empresas')) {
    currentPage = 'empresas';
  } else if (currentPath.includes('/usuarios')) {
    currentPage = 'usuarios';
  } else if (currentPath.includes('/escritorios')) {
    currentPage = 'escritorios';
  }

  // Remover classe active de todos os itens
  navItems.forEach((item) => {
    item.classList.remove('active');

    // Adicionar classe active ao item correspondente à página atual ou ao seu pai
    if (item.dataset.page === currentPage || item.dataset.page === parentPage) {
      item.classList.add('active');

      // Se for um dropdown, abri-lo apenas se a sidebar não estiver colapsada
      if (item.classList.contains('nav-dropdown')) {
        const container = document.querySelector('.dashboard-container');
        // Só abrir o dropdown se a sidebar NÃO estiver colapsada
        if (container && !container.classList.contains('sidebar-collapsed')) {
          item.classList.add('open');
        } else {
          // Se a sidebar estiver colapsada, garantir que o dropdown esteja fechado
          item.classList.remove('open');
        }
      }
    }
  });

  // Marcar o item do dropdown ativo
  dropdownItems.forEach((item) => {
    item.classList.remove('active');

    if (item.dataset.page === currentPage) {
      item.classList.add('active');
    }
  });

  // Configurar os toggles dos dropdowns
  document.querySelectorAll('.nav-dropdown > a').forEach((toggle) => {
    // Remover event listeners anteriores (se possível)
    const newToggle = toggle.cloneNode(true);
    toggle.parentNode.replaceChild(newToggle, toggle);

    newToggle.addEventListener('click', function (e) {
      e.preventDefault();
      const parent = this.closest('.nav-dropdown');

      // Fechar outros dropdowns
      document.querySelectorAll('.nav-dropdown').forEach((dropdown) => {
        if (dropdown !== parent) {
          dropdown.classList.remove('open');
        }
      });

      // Alternar o estado do dropdown atual
      parent.classList.toggle('open');
    });
  });

  // Configurar os links do dropdown do perfil
  setupProfileDropdownLinks();
}
