-- Esquema para o sistema de importação XML

-- Tabela de Clientes (Destinatários)
CREATE TABLE IF NOT EXISTS cliente (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cnpj VARCHAR(18) NOT NULL,
    razao_social VARCHAR(255) NOT NULL,
    inscricao_estadual VARCHAR(30),
    logradouro VARCHAR(255),
    numero VARCHAR(20),
    bairro VARCHAR(100),
    municipio VARCHAR(100),
    uf VARCHAR(2),
    cep VARCHAR(10),
    pais VARCHAR(50),
    codigo_pais VARCHAR(10),
    cnae VARCHAR(20),                      -- Novo campo para CNAE
    atividade VARCHAR(50),                 -- Novo campo para atividade (Indústria, Comércio, etc.)
    destinacao VARCHAR(50),                -- Novo campo para destinação (Industrialização, Revenda, etc.)
    simples_nacional BOOLEAN DEFAULT FALSE, -- Novo campo para indicar se é Simples Nacional
    ind_ie_dest VARCHAR(2),                -- Novo campo para indIEDest do XML
    ind_final VARCHAR(2),                  -- Novo campo para indFinal do XML
    data_cadastro TIMESTAMP DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'novo', -- 'novo', 'producao'
    UNIQUE (empresa_id, cnpj)
);

-- Tabela de Produtos
CREATE TABLE IF NOT EXISTS produto (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    codigo VARCHAR(50) NOT NULL,
    descricao VARCHAR(255) NOT NULL,
    ncm VARCHAR(20),
    cfop VARCHAR(10),
    unidade_comercial VARCHAR(10),
    unidade_tributavel VARCHAR(10),
    codigo_ean VARCHAR(50),        -- Campo para cEAN
    codigo_ean_tributavel VARCHAR(50), -- Campo para cEANTrib
    unidade_tributaria VARCHAR(10),    -- Campo para uTrib
    data_cadastro TIMESTAMP DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'novo' -- 'novo', 'producao', 'inconsistente', 'conforme'
    -- Removida a restrição UNIQUE para permitir produtos duplicados em importações
);

-- Tabela de Tributos
CREATE TABLE IF NOT EXISTS tributo (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,
    data_emissao DATE NOT NULL,
    data_saida DATE,

    -- ICMS
    icms_origem VARCHAR(2),
    icms_cst VARCHAR(3),
    icms_mod_bc VARCHAR(2),
    icms_p_red_bc DECIMAL(10, 4),
    icms_vbc DECIMAL(10, 2),
    icms_aliquota DECIMAL(10, 4),
    icms_valor DECIMAL(10, 2),
    icms_v_op DECIMAL(10, 2),       -- Novo campo para vICMSOp
    icms_p_dif DECIMAL(10, 4),      -- Novo campo para pDif
    icms_v_dif DECIMAL(10, 2),      -- Novo campo para vICMSDif

    -- ICMS-ST
    icms_st_mod_bc VARCHAR(2),      -- modBCST
    icms_st_p_mva DECIMAL(10, 4),   -- pMVAST
    icms_st_vbc DECIMAL(10, 2),     -- vBCST
    icms_st_aliquota DECIMAL(10, 4), -- pICMSST
    icms_st_valor DECIMAL(10, 2),   -- vICMSST

    -- IPI
    ipi_cst VARCHAR(3),
    ipi_vbc DECIMAL(10, 2),
    ipi_aliquota DECIMAL(10, 4),
    ipi_valor DECIMAL(10, 2),
    ipi_codigo_enquadramento VARCHAR(3),  -- Novo campo para cEnq

    -- PIS
    pis_cst VARCHAR(3),
    pis_vbc DECIMAL(10, 2),
    pis_aliquota DECIMAL(10, 4),
    pis_valor DECIMAL(10, 2),

    -- COFINS
    cofins_cst VARCHAR(3),
    cofins_vbc DECIMAL(10, 2),
    cofins_aliquota DECIMAL(10, 4),
    cofins_valor DECIMAL(10, 2),

    -- DIFAL (Diferencial de Alíquota)
    difal_vbc DECIMAL(10, 2),                  -- vBC
    difal_p_fcp_uf_dest DECIMAL(10, 4),        -- pFCPUFDest
    difal_p_icms_uf_dest DECIMAL(10, 4),       -- pICMSUFDest
    difal_p_icms_inter DECIMAL(10, 4),         -- pICMSInter
    difal_p_icms_inter_part DECIMAL(10, 4),    -- pICMSInterPart
    difal_v_fcp_uf_dest DECIMAL(10, 2),        -- vFCPUFDest
    difal_v_icms_uf_dest DECIMAL(10, 2),       -- vICMSUFDest
    difal_v_icms_uf_remet DECIMAL(10, 2),      -- vICMSUFRemet

    -- Informações adicionais
    numero_nf VARCHAR(20),
    chave_nf VARCHAR(50),
    status VARCHAR(20) DEFAULT 'novo', -- 'novo', 'producao', 'inconsistente', 'conforme'

    -- Status específicos para cada tipo de tributo
    icms_status VARCHAR(20) DEFAULT 'novo', -- 'novo', 'producao', 'inconsistente', 'conforme'
    icms_st_status VARCHAR(20) DEFAULT 'novo', -- 'novo', 'producao', 'inconsistente', 'conforme'
    ipi_status VARCHAR(20) DEFAULT 'novo', -- 'novo', 'producao', 'inconsistente', 'conforme'
    pis_status VARCHAR(20) DEFAULT 'novo', -- 'novo', 'producao', 'inconsistente', 'conforme'
    cofins_status VARCHAR(20) DEFAULT 'novo', -- 'novo', 'producao', 'inconsistente', 'conforme'
    difal_status VARCHAR(20) DEFAULT 'novo', -- 'novo', 'producao', 'inconsistente', 'conforme'

    -- Valores do produto
    quantidade DECIMAL(10, 4),
    valor_unitario DECIMAL(10, 4),
    valor_total DECIMAL(10, 2),

    data_cadastro TIMESTAMP DEFAULT NOW(),

    UNIQUE (empresa_id, cliente_id, produto_id, data_emissao, numero_nf)
);

-- Tabela para armazenar as importações realizadas
CREATE TABLE IF NOT EXISTS importacao_xml (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    usuario_id INTEGER REFERENCES usuario(id) NOT NULL,
    arquivo_nome VARCHAR(255) NOT NULL,
    chave_nf VARCHAR(50),
    numero_nf VARCHAR(20),
    data_emissao DATE,
    cnpj_emitente VARCHAR(18),
    razao_social_emitente VARCHAR(255),
    data_importacao TIMESTAMP DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'concluido', -- 'concluido', 'erro'
    mensagem TEXT
);

-- Tabela para armazenar o histórico de alterações de tributos
CREATE TABLE IF NOT EXISTS tributo_historico (
    id SERIAL PRIMARY KEY,
    tributo_id INTEGER REFERENCES tributo(id) NOT NULL,
    usuario_id INTEGER REFERENCES usuario(id) NOT NULL,
    tipo_tributo VARCHAR(20) NOT NULL, -- 'icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal'
    status_anterior VARCHAR(20),
    status_novo VARCHAR(20),
    valores_anteriores JSONB, -- Armazena os valores anteriores em formato JSON
    valores_novos JSONB, -- Armazena os novos valores em formato JSON
    data_alteracao TIMESTAMP DEFAULT NOW()
);

-- Índices para melhorar a performance
CREATE INDEX IF NOT EXISTS idx_cliente_empresa ON cliente(empresa_id);
CREATE INDEX IF NOT EXISTS idx_cliente_escritorio ON cliente(escritorio_id);
CREATE INDEX IF NOT EXISTS idx_produto_empresa ON produto(empresa_id);
CREATE INDEX IF NOT EXISTS idx_produto_escritorio ON produto(escritorio_id);
CREATE INDEX IF NOT EXISTS idx_tributo_empresa ON tributo(empresa_id);
CREATE INDEX IF NOT EXISTS idx_tributo_escritorio ON tributo(escritorio_id);
CREATE INDEX IF NOT EXISTS idx_tributo_cliente ON tributo(cliente_id);
CREATE INDEX IF NOT EXISTS idx_tributo_produto ON tributo(produto_id);
CREATE INDEX IF NOT EXISTS idx_tributo_status ON tributo(status);
CREATE INDEX IF NOT EXISTS idx_tributo_icms_status ON tributo(icms_status);
CREATE INDEX IF NOT EXISTS idx_tributo_icms_st_status ON tributo(icms_st_status);
CREATE INDEX IF NOT EXISTS idx_tributo_ipi_status ON tributo(ipi_status);
CREATE INDEX IF NOT EXISTS idx_tributo_pis_status ON tributo(pis_status);
CREATE INDEX IF NOT EXISTS idx_tributo_cofins_status ON tributo(cofins_status);
CREATE INDEX IF NOT EXISTS idx_tributo_difal_status ON tributo(difal_status);
CREATE INDEX IF NOT EXISTS idx_importacao_empresa ON importacao_xml(empresa_id);
CREATE INDEX IF NOT EXISTS idx_importacao_escritorio ON importacao_xml(escritorio_id);
CREATE INDEX IF NOT EXISTS idx_importacao_usuario ON importacao_xml(usuario_id);
CREATE INDEX IF NOT EXISTS idx_tributo_historico_tributo ON tributo_historico(tributo_id);
CREATE INDEX IF NOT EXISTS idx_tributo_historico_usuario ON tributo_historico(usuario_id);
CREATE INDEX IF NOT EXISTS idx_tributo_historico_tipo ON tributo_historico(tipo_tributo);
CREATE INDEX IF NOT EXISTS idx_tributo_historico_data ON tributo_historico(data_alteracao);

-- Comentários nas tabelas
COMMENT ON TABLE cliente IS 'Armazena informações dos clientes (destinatários) extraídas dos XMLs';
COMMENT ON TABLE produto IS 'Armazena informações dos produtos extraídas dos XMLs';
COMMENT ON TABLE tributo IS 'Armazena informações dos tributos por produto e cliente';
COMMENT ON TABLE importacao_xml IS 'Registra as importações de XML realizadas';
