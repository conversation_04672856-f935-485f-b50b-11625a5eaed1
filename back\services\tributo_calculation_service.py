from models import db, Tributo, Produto, CenarioICMS, CenarioICMSST, CenarioIPI, CenarioPIS, CenarioCOFINS, CenarioDIFAL
import logging
from decimal import Decimal

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TributoCalculationService:
    """
    Serviço para cálculo de tributos com base nos cenários em produção
    """

    def __init__(self, empresa_id):
        """
        Inicializa o serviço de cálculo de tributos

        Args:
            empresa_id (int): ID da empresa
        """
        self.empresa_id = empresa_id

    def calculate_tributos(self, tributo_ids=None, produto_ids=None):
        """
        Calcula os valores de tributos com base nos cenários em produção

        Args:
            tributo_ids (list): Lista de IDs de tributos para calcular (opcional)
            produto_ids (list): Lista de IDs de produtos para calcular (opcional)

        Returns:
            dict: Resultado do cálculo
        """
        # Construir query base
        query = Tributo.query.filter_by(empresa_id=self.empresa_id)

        # Aplicar filtros
        if tributo_ids:
            query = query.filter(Tributo.id.in_(tributo_ids))
        if produto_ids:
            query = query.filter(Tributo.produto_id.in_(produto_ids))

        # Executar a query
        tributos = query.all()

        # Resultados
        results = {
            'total': len(tributos),
            'calculados': 0,
            'nao_calculados': 0,
            'por_tipo': {
                'icms': {'calculados': 0, 'nao_calculados': 0},
                'icms_st': {'calculados': 0, 'nao_calculados': 0},
                'ipi': {'calculados': 0, 'nao_calculados': 0},
                'pis': {'calculados': 0, 'nao_calculados': 0},
                'cofins': {'calculados': 0, 'nao_calculados': 0},
                'difal': {'calculados': 0, 'nao_calculados': 0}
            }
        }

        # Processar cada tributo
        for tributo in tributos:
            # Calcular para cada tipo de tributo
            tipos_tributo = ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal']
            all_calculated = True

            for tipo in tipos_tributo:
                # Verificar se o tributo tem valores para este tipo
                if self._has_tributo_values(tributo, tipo):
                    # Calcular valores com base no cenário em produção
                    calculated = self._calculate_tributo_values(tributo, tipo)

                    if calculated:
                        results['por_tipo'][tipo]['calculados'] += 1
                    else:
                        results['por_tipo'][tipo]['nao_calculados'] += 1
                        all_calculated = False

            # Atualizar contadores
            if all_calculated:
                results['calculados'] += 1
                # Atualizar status do produto para 'conforme'
                produto = db.session.get(Produto, tributo.produto_id)
                if produto:
                    produto.status = 'conforme'
            else:
                results['nao_calculados'] += 1

        # Salvar alterações
        db.session.commit()

        return results

    def _has_tributo_values(self, tributo, tipo_tributo):
        """
        Verifica se o tributo tem valores para um tipo específico

        Args:
            tributo (Tributo): Objeto do tributo
            tipo_tributo (str): Tipo de tributo

        Returns:
            bool: True se o tributo tem valores para o tipo, False caso contrário
        """
        if tipo_tributo == 'icms':
            return tributo.icms_valor is not None
        elif tipo_tributo == 'icms_st':
            return tributo.icms_st_valor is not None
        elif tipo_tributo == 'ipi':
            return tributo.ipi_valor is not None
        elif tipo_tributo == 'pis':
            return tributo.pis_valor is not None
        elif tipo_tributo == 'cofins':
            return tributo.cofins_valor is not None
        elif tipo_tributo == 'difal':
            return tributo.difal_v_icms_uf_dest is not None
        return False

    def _calculate_tributo_values(self, tributo, tipo_tributo):
        """
        Calcula os valores de um tributo com base no cenário em produção

        Args:
            tributo (Tributo): Objeto do tributo
            tipo_tributo (str): Tipo de tributo

        Returns:
            bool: True se o cálculo foi realizado, False caso contrário
        """
        # Obter o modelo correspondente
        CenarioModel = {
            'icms': CenarioICMS,
            'icms_st': CenarioICMSST,
            'ipi': CenarioIPI,
            'pis': CenarioPIS,
            'cofins': CenarioCOFINS,
            'difal': CenarioDIFAL
        }[tipo_tributo]

        # Buscar cenário em produção ativo
        cenario = CenarioModel.query.filter_by(
            empresa_id=tributo.empresa_id,
            cliente_id=tributo.cliente_id,
            produto_id=tributo.produto_id,
            status='producao',
            ativo=True
        ).first()

        # Se não encontrar cenário ativo, verificar se existe algum em produção
        if not cenario:
            cenario = CenarioModel.query.filter_by(
                empresa_id=tributo.empresa_id,
                cliente_id=tributo.cliente_id,
                produto_id=tributo.produto_id,
                status='producao'
            ).first()

        # Se não encontrar nenhum cenário em produção, não calcular
        if not cenario:
            return False

        # Calcular valores com base no cenário
        if tipo_tributo == 'icms':
            return self._calculate_icms(tributo, cenario)
        elif tipo_tributo == 'icms_st':
            return self._calculate_icms_st(tributo, cenario)
        elif tipo_tributo == 'ipi':
            return self._calculate_ipi(tributo, cenario)
        elif tipo_tributo == 'pis':
            return self._calculate_pis(tributo, cenario)
        elif tipo_tributo == 'cofins':
            return self._calculate_cofins(tributo, cenario)
        elif tipo_tributo == 'difal':
            return self._calculate_difal(tributo, cenario)

        return False

    def _calculate_icms(self, tributo, cenario):
        """Calcula ICMS"""
        if cenario.aliquota is not None and tributo.valor_total is not None:
            # Aplicar redução da base de cálculo, se houver
            base_calculo = float(tributo.valor_total)
            if cenario.p_red_bc is not None and float(cenario.p_red_bc) > 0:
                base_calculo = base_calculo * (1 - float(cenario.p_red_bc) / 100)

            # Calcular valor do ICMS
            valor_calculado = (base_calculo * float(cenario.aliquota)) / 100

            return True
        return False

    def _calculate_icms_st(self, tributo, cenario):
        """Calcula ICMS-ST"""
        if cenario.icms_st_aliquota is not None and tributo.valor_total is not None:
            # Aplicar redução da base de cálculo, se houver
            base_calculo = float(tributo.valor_total)
            if cenario.p_red_bc is not None and float(cenario.p_red_bc) > 0:
                base_calculo = base_calculo * (1 - float(cenario.p_red_bc) / 100)

            # Aplicar MVA, se houver
            if cenario.icms_st_p_mva is not None and float(cenario.icms_st_p_mva) > 0:
                base_calculo = base_calculo * (1 + float(cenario.icms_st_p_mva) / 100)

            # Calcular valor do ICMS-ST
            valor_calculado = (base_calculo * float(cenario.icms_st_aliquota)) / 100

            # Atualizar valores calculados no tributo
            tributo.icms_st_calculado_aliquota = cenario.icms_st_aliquota
            tributo.icms_st_calculado_valor = valor_calculado

            return True
        return False

    def _calculate_ipi(self, tributo, cenario):
        """Calcula IPI"""
        if cenario.aliquota is not None and tributo.valor_total is not None:
            # Calcular valor do IPI
            valor_calculado = (float(tributo.valor_total) * float(cenario.aliquota)) / 100

            # Atualizar valores calculados no tributo
            tributo.ipi_calculado_aliquota = cenario.aliquota
            tributo.ipi_calculado_valor = valor_calculado
            tributo.ipi_ex = cenario.ex

            return True
        return False

    def _calculate_pis(self, tributo, cenario):
        """Calcula PIS"""
        if cenario.aliquota is not None and tributo.valor_total is not None:
            # Aplicar redução da base de cálculo, se houver
            base_calculo = float(tributo.valor_total)
            if cenario.p_red_bc is not None and float(cenario.p_red_bc) > 0:
                base_calculo = base_calculo * (1 - float(cenario.p_red_bc) / 100)

            # Calcular valor do PIS
            valor_calculado = (base_calculo * float(cenario.aliquota)) / 100

            # Atualizar valores calculados no tributo
            tributo.pis_calculado_aliquota = cenario.aliquota
            tributo.pis_calculado_valor = valor_calculado
            tributo.pis_p_red_bc = cenario.p_red_bc

            return True
        return False

    def _calculate_cofins(self, tributo, cenario):
        """Calcula COFINS"""
        if cenario.aliquota is not None and tributo.valor_total is not None:
            # Aplicar redução da base de cálculo, se houver
            base_calculo = float(tributo.valor_total)
            if cenario.p_red_bc is not None and float(cenario.p_red_bc) > 0:
                base_calculo = base_calculo * (1 - float(cenario.p_red_bc) / 100)

            # Calcular valor do COFINS
            valor_calculado = (base_calculo * float(cenario.aliquota)) / 100

            # Atualizar valores calculados no tributo
            tributo.cofins_calculado_aliquota = cenario.aliquota
            tributo.cofins_calculado_valor = valor_calculado
            tributo.cofins_p_red_bc = cenario.p_red_bc

            return True
        return False

    def _calculate_difal(self, tributo, cenario):
        """Calcula DIFAL"""
        if (cenario.p_icms_uf_dest is not None and
            cenario.p_icms_inter is not None and
            tributo.valor_total is not None):

            # Aplicar redução da base de cálculo, se houver
            base_calculo = float(tributo.valor_total)
            if cenario.p_red_bc is not None and float(cenario.p_red_bc) > 0:
                base_calculo = base_calculo * (1 - float(cenario.p_red_bc) / 100)

            # Calcular diferença entre alíquotas
            diferenca = float(cenario.p_icms_uf_dest) - float(cenario.p_icms_inter)

            # Aplicar percentual de partilha
            if cenario.p_icms_inter_part is not None:
                diferenca = diferenca * float(cenario.p_icms_inter_part) / 100

            # Calcular valor do DIFAL
            valor_calculado = (base_calculo * diferenca) / 100

            # Calcular FCP, se houver
            if cenario.p_fcp_uf_dest is not None and float(cenario.p_fcp_uf_dest) > 0:
                fcp = (base_calculo * float(cenario.p_fcp_uf_dest)) / 100
                valor_calculado += fcp

            # Atualizar valor calculado no tributo
            tributo.difal_calculado_valor = valor_calculado

            return True
        return False

    # Métodos estáticos para cálculo de tributos (utilizados pelo serviço de auditoria)

    @staticmethod
    def calcular_ipi(valor_total, cenario_ipi, valor_frete=None, valor_desconto=None):
        """
        Calcula o IPI para um tributo

        Args:
            valor_total (Decimal): Valor total da mercadoria
            cenario_ipi (CenarioIPI): Cenário de IPI
            valor_frete (Decimal, optional): Valor do frete
            valor_desconto (Decimal, optional): Valor do desconto

        Returns:
            tuple: (base_calculo, valor_ipi)
        """
        # Verificar se o CST é 50 (tributado)
        if cenario_ipi.cst != '50':
            logger.info(f"CST de IPI não é 50 (tributado)")
            return Decimal('0.00'), Decimal('0.00')

        # Calcular base de cálculo (valor da mercadoria)
        base_calculo = Decimal(str(valor_total)) if valor_total else Decimal('0.00')

        # Subtrair desconto da base de cálculo se configurado no cenário
        if valor_desconto and hasattr(cenario_ipi, 'incluir_desconto') and cenario_ipi.incluir_desconto:
            base_calculo -= Decimal(str(valor_desconto))

        # Incluir frete na base de cálculo se configurado no cenário
        if valor_frete and hasattr(cenario_ipi, 'incluir_frete') and cenario_ipi.incluir_frete:
            base_calculo += Decimal(str(valor_frete))

        # Calcular valor do IPI
        aliquota = Decimal(str(cenario_ipi.aliquota)) if cenario_ipi.aliquota else Decimal('0.00')
        valor_ipi = (base_calculo * aliquota) / Decimal('100.00')

        logger.info(f"Cálculo de IPI: BC={base_calculo}, Alíquota={aliquota}, Valor={valor_ipi}")

        return base_calculo, valor_ipi

    @staticmethod
    def calcular_icms(valor_total, cenario_icms, valor_ipi=None, cliente_uso_consumo_ativo=False, valor_frete=None, valor_desconto=None):
        """
        Calcula o ICMS para um tributo

        Args:
            valor_total (Decimal): Valor total da mercadoria
            cenario_icms (CenarioICMS): Cenário de ICMS
            valor_ipi (Decimal, optional): Valor do IPI
            cliente_uso_consumo_ativo (bool, optional): Indica se o cliente é de uso e consumo ou ativo imobilizado
            valor_frete (Decimal, optional): Valor do frete
            valor_desconto (Decimal, optional): Valor do desconto

        Returns:
            tuple: (base_calculo, valor_icms)
        """
        # Verificar se o CST é 00, 10, 20 ou 70 (tributado)
        csts_tributados = ['00', '10', '20', '70']
        if cenario_icms.cst not in csts_tributados:
            logger.info(f"CST de ICMS não é tributado")
            return Decimal('0.00'), Decimal('0.00')

        # Calcular base de cálculo
        base_calculo = Decimal(str(valor_total)) if valor_total else Decimal('0.00')

        # Subtrair desconto da base de cálculo se configurado no cenário
        if valor_desconto and hasattr(cenario_icms, 'incluir_desconto') and cenario_icms.incluir_desconto:
            base_calculo -= Decimal(str(valor_desconto))

        # Incluir frete na base de cálculo se configurado no cenário
        if valor_frete and hasattr(cenario_icms, 'incluir_frete') and cenario_icms.incluir_frete:
            base_calculo += Decimal(str(valor_frete))

        # Adicionar valor do IPI à base de cálculo se for cliente de uso e consumo ou ativo imobilizado
        if cliente_uso_consumo_ativo and valor_ipi:
            base_calculo += Decimal(str(valor_ipi))

        # Aplicar redução da base de cálculo, se houver
        if cenario_icms.p_red_bc and Decimal(str(cenario_icms.p_red_bc)) > Decimal('0.00'):
            valor_reducao = base_calculo * (Decimal(str(cenario_icms.p_red_bc)) / Decimal('100.00'))
            base_calculo -= valor_reducao

        # Calcular valor do ICMS
        aliquota = Decimal(str(cenario_icms.aliquota)) if cenario_icms.aliquota else Decimal('0.00')
        valor_icms = (base_calculo * aliquota) / Decimal('100.00')

        logger.info(f"Cálculo de ICMS: BC={base_calculo}, Alíquota={aliquota}, Valor={valor_icms}")

        return base_calculo, valor_icms

    @staticmethod
    def calcular_icms_st(valor_total, cenario_icms_st, valor_ipi=None, valor_icms=None, cliente_uso_consumo_ativo=False, valor_frete=None, valor_desconto=None):
        """
        Calcula o ICMS-ST para um tributo

        Args:
            valor_total (Decimal): Valor total da mercadoria
            cenario_icms_st (CenarioICMSST): Cenário de ICMS-ST
            valor_ipi (Decimal, optional): Valor do IPI
            valor_icms (Decimal, optional): Valor do ICMS
            cliente_uso_consumo_ativo (bool, optional): Indica se o cliente é de uso e consumo ou ativo imobilizado
            valor_frete (Decimal, optional): Valor do frete
            valor_desconto (Decimal, optional): Valor do desconto

        Returns:
            tuple: (base_calculo, valor_icms_st)
        """
        # Verificar se o CST é 10 ou 70 (tributado com ST)
        csts_st = ['10', '70']
        if cenario_icms_st.cst not in csts_st:
            logger.info(f"CST de ICMS-ST não é 10 ou 70")
            return Decimal('0.00'), Decimal('0.00')

        # Calcular base de cálculo
        base_calculo = Decimal(str(valor_total)) if valor_total else Decimal('0.00')

        # Subtrair desconto da base de cálculo se configurado no cenário
        if valor_desconto and hasattr(cenario_icms_st, 'incluir_desconto') and cenario_icms_st.incluir_desconto:
            base_calculo -= Decimal(str(valor_desconto))

        # Incluir frete na base de cálculo se configurado no cenário
        if valor_frete and hasattr(cenario_icms_st, 'incluir_frete') and cenario_icms_st.incluir_frete:
            base_calculo += Decimal(str(valor_frete))

        # Adicionar valor do IPI à base de cálculo se for cliente de uso e consumo ou ativo imobilizado
        if cliente_uso_consumo_ativo and valor_ipi:
            base_calculo += Decimal(str(valor_ipi))

        # Aplicar MVA
        if cenario_icms_st.icms_st_p_mva and Decimal(str(cenario_icms_st.icms_st_p_mva)) > Decimal('0.00'):
            base_calculo = base_calculo * (Decimal('1.00') + Decimal(str(cenario_icms_st.icms_st_p_mva)) / Decimal('100.00'))

        # Aplicar redução da base de cálculo, se houver e se o CST não for 10
        if cenario_icms_st.cst != '10' and cenario_icms_st.p_red_bc and Decimal(str(cenario_icms_st.p_red_bc)) > Decimal('0.00'):
            valor_reducao = base_calculo * (Decimal(str(cenario_icms_st.p_red_bc)) / Decimal('100.00'))
            base_calculo -= valor_reducao

        # Calcular valor do ICMS-ST
        aliquota = Decimal(str(cenario_icms_st.icms_st_aliquota)) if cenario_icms_st.icms_st_aliquota else Decimal('0.00')
        valor_icms_st_total = (base_calculo * aliquota) / Decimal('100.00')

        # Subtrair o valor do ICMS próprio
        valor_icms_st = valor_icms_st_total - Decimal(str(valor_icms)) if valor_icms else valor_icms_st_total

        logger.info(f"Cálculo de ICMS-ST: BC={base_calculo}, Alíquota={aliquota}, Valor={valor_icms_st}")

        return base_calculo, valor_icms_st

    @staticmethod
    def calcular_pis(valor_total, cenario_pis, valor_icms=None, valor_frete=None, valor_desconto=None):
        """
        Calcula o PIS para um tributo

        Args:
            valor_total (Decimal): Valor total da mercadoria
            cenario_pis (CenarioPIS): Cenário de PIS
            valor_icms (Decimal, optional): Valor do ICMS
            valor_frete (Decimal, optional): Valor do frete
            valor_desconto (Decimal, optional): Valor do desconto

        Returns:
            tuple: (base_calculo, valor_pis)
        """
        # Verificar se o CST é 1 ou 2 (tributado)
        csts_tributados = ['01', '02', '1', '2']
        if cenario_pis.cst not in csts_tributados:
            logger.info(f"CST de PIS não é 01 ou 02")
            return Decimal('0.00'), Decimal('0.00')

        # Se o CST for 4, a alíquota deve ser 0
        if cenario_pis.cst in ['04', '4']:
            logger.info(f"CST de PIS é 04, alíquota deve ser 0")
            return Decimal('0.00'), Decimal('0.00')

        # Calcular base de cálculo
        base_calculo = Decimal(str(valor_total)) if valor_total else Decimal('0.00')

        # Subtrair desconto da base de cálculo se configurado no cenário
        if valor_desconto and hasattr(cenario_pis, 'incluir_desconto') and cenario_pis.incluir_desconto:
            base_calculo -= Decimal(str(valor_desconto))

        # Incluir frete na base de cálculo se configurado no cenário
        if valor_frete and hasattr(cenario_pis, 'incluir_frete') and cenario_pis.incluir_frete:
            base_calculo += Decimal(str(valor_frete))

        # Subtrair o valor do ICMS da base de cálculo
        if valor_icms:
            base_calculo -= Decimal(str(valor_icms))

        # Aplicar redução da base de cálculo, se houver
        if cenario_pis.p_red_bc and Decimal(str(cenario_pis.p_red_bc)) > Decimal('0.00'):
            base_calculo = base_calculo * (Decimal('1.00') - Decimal(str(cenario_pis.p_red_bc)) / Decimal('100.00'))

        # Calcular valor do PIS
        aliquota = Decimal(str(cenario_pis.aliquota)) if cenario_pis.aliquota else Decimal('0.00')
        valor_pis = (base_calculo * aliquota) / Decimal('100.00')

        logger.info(f"Cálculo de PIS: BC={base_calculo}, Alíquota={aliquota}, Valor={valor_pis}")

        return base_calculo, valor_pis

    @staticmethod
    def calcular_cofins(valor_total, cenario_cofins, valor_icms=None, valor_frete=None, valor_desconto=None):
        """
        Calcula o COFINS para um tributo

        Args:
            valor_total (Decimal): Valor total da mercadoria
            cenario_cofins (CenarioCOFINS): Cenário de COFINS
            valor_icms (Decimal, optional): Valor do ICMS
            valor_frete (Decimal, optional): Valor do frete
            valor_desconto (Decimal, optional): Valor do desconto

        Returns:
            tuple: (base_calculo, valor_cofins)
        """
        # Verificar se o CST é 1 ou 2 (tributado)
        csts_tributados = ['01', '02', '1', '2']
        if cenario_cofins.cst not in csts_tributados:
            logger.info(f"CST de COFINS não é 01 ou 02")
            return Decimal('0.00'), Decimal('0.00')

        # Se o CST for 4, a alíquota deve ser 0
        if cenario_cofins.cst in ['04', '4']:
            logger.info(f"CST de COFINS é 04, alíquota deve ser 0")
            return Decimal('0.00'), Decimal('0.00')

        # Calcular base de cálculo
        base_calculo = Decimal(str(valor_total)) if valor_total else Decimal('0.00')

        # Subtrair desconto da base de cálculo se configurado no cenário
        if valor_desconto and hasattr(cenario_cofins, 'incluir_desconto') and cenario_cofins.incluir_desconto:
            base_calculo -= Decimal(str(valor_desconto))

        # Incluir frete na base de cálculo se configurado no cenário
        if valor_frete and hasattr(cenario_cofins, 'incluir_frete') and cenario_cofins.incluir_frete:
            base_calculo += Decimal(str(valor_frete))

        # Subtrair o valor do ICMS da base de cálculo
        if valor_icms:
            base_calculo -= Decimal(str(valor_icms))

        # Aplicar redução da base de cálculo, se houver
        if cenario_cofins.p_red_bc and Decimal(str(cenario_cofins.p_red_bc)) > Decimal('0.00'):
            base_calculo = base_calculo * (Decimal('1.00') - Decimal(str(cenario_cofins.p_red_bc)) / Decimal('100.00'))

        # Calcular valor do COFINS
        aliquota = Decimal(str(cenario_cofins.aliquota)) if cenario_cofins.aliquota else Decimal('0.00')
        valor_cofins = (base_calculo * aliquota) / Decimal('100.00')

        logger.info(f"Cálculo de COFINS: BC={base_calculo}, Alíquota={aliquota}, Valor={valor_cofins}")

        return base_calculo, valor_cofins

    @staticmethod
    def calcular_difal(valor_total, cenario_difal):
        """
        Calcula o DIFAL para um tributo

        Args:
            valor_total (Decimal): Valor total da mercadoria
            cenario_difal (CenarioDIFAL): Cenário de DIFAL

        Returns:
            Decimal: valor_difal
        """
        # Implementação parcial do DIFAL
        # TODO: Implementar cálculo completo do DIFAL

        logger.info(f"Cálculo de DIFAL não implementado completamente")

        return Decimal('0.00')
