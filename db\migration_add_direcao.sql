-- Migration to add direcao column to all cenario tables
-- This column will store the direction of the operation (entrada or saida)

-- Add the column to the cenario_icms table
ALTER TABLE cenario_icms ADD COLUMN IF NOT EXISTS direcao VARCHAR(10);

-- Add the column to the cenario_icms_st table
ALTER TABLE cenario_icms_st ADD COLUMN IF NOT EXISTS direcao VARCHAR(10);

-- Add the column to the cenario_ipi table
ALTER TABLE cenario_ipi ADD COLUMN IF NOT EXISTS direcao VARCHAR(10);

-- Add the column to the cenario_pis table
ALTER TABLE cenario_pis ADD COLUMN IF NOT EXISTS direcao VARCHAR(10);

-- Add the column to the cenario_cofins table
ALTER TABLE cenario_cofins ADD COLUMN IF NOT EXISTS direcao VARCHAR(10);

-- Add the column to the cenario_difal table
ALTER TABLE cenario_difal ADD COLUMN IF NOT EXISTS direcao VARCHAR(10);

-- Add comments to explain the column
COMMENT ON COLUMN cenario_icms.direcao IS 'Direção da operação: entrada ou saída';
COMMENT ON COLUMN cenario_icms_st.direcao IS 'Direção da operação: entrada ou saída';
COMMENT ON COLUMN cenario_ipi.direcao IS 'Direção da operação: entrada ou saída';
COMMENT ON COLUMN cenario_pis.direcao IS 'Direção da operação: entrada ou saída';
COMMENT ON COLUMN cenario_cofins.direcao IS 'Direção da operação: entrada ou saída';
COMMENT ON COLUMN cenario_difal.direcao IS 'Direção da operação: entrada ou saída';

-- Create indexes for better performance when filtering by direcao
CREATE INDEX IF NOT EXISTS idx_cenario_icms_direcao ON cenario_icms(direcao);
CREATE INDEX IF NOT EXISTS idx_cenario_icms_st_direcao ON cenario_icms_st(direcao);
CREATE INDEX IF NOT EXISTS idx_cenario_ipi_direcao ON cenario_ipi(direcao);
CREATE INDEX IF NOT EXISTS idx_cenario_pis_direcao ON cenario_pis(direcao);
CREATE INDEX IF NOT EXISTS idx_cenario_cofins_direcao ON cenario_cofins(direcao);
CREATE INDEX IF NOT EXISTS idx_cenario_difal_direcao ON cenario_difal(direcao);

-- Update existing data based on tributo.tipo_operacao
-- For each cenario, find the corresponding tributo and set direcao based on tipo_operacao
-- If tipo_operacao is 0, set direcao to 'entrada'
-- If tipo_operacao is 1, set direcao to 'saida'

-- Update cenario_icms
UPDATE cenario_icms c
SET direcao = CASE 
    WHEN EXISTS (
        SELECT 1 FROM tributo t 
        WHERE t.produto_id = c.produto_id 
        AND t.cliente_id = c.cliente_id 
        AND t.tipo_operacao = '0'
    ) THEN 'entrada'
    ELSE 'saida'
END;

-- Update cenario_icms_st
UPDATE cenario_icms_st c
SET direcao = CASE 
    WHEN EXISTS (
        SELECT 1 FROM tributo t 
        WHERE t.produto_id = c.produto_id 
        AND t.cliente_id = c.cliente_id 
        AND t.tipo_operacao = '0'
    ) THEN 'entrada'
    ELSE 'saida'
END;

-- Update cenario_ipi
UPDATE cenario_ipi c
SET direcao = CASE 
    WHEN EXISTS (
        SELECT 1 FROM tributo t 
        WHERE t.produto_id = c.produto_id 
        AND t.cliente_id = c.cliente_id 
        AND t.tipo_operacao = '0'
    ) THEN 'entrada'
    ELSE 'saida'
END;

-- Update cenario_pis
UPDATE cenario_pis c
SET direcao = CASE 
    WHEN EXISTS (
        SELECT 1 FROM tributo t 
        WHERE t.produto_id = c.produto_id 
        AND t.cliente_id = c.cliente_id 
        AND t.tipo_operacao = '0'
    ) THEN 'entrada'
    ELSE 'saida'
END;

-- Update cenario_cofins
UPDATE cenario_cofins c
SET direcao = CASE 
    WHEN EXISTS (
        SELECT 1 FROM tributo t 
        WHERE t.produto_id = c.produto_id 
        AND t.cliente_id = c.cliente_id 
        AND t.tipo_operacao = '0'
    ) THEN 'entrada'
    ELSE 'saida'
END;

-- Update cenario_difal
UPDATE cenario_difal c
SET direcao = CASE 
    WHEN EXISTS (
        SELECT 1 FROM tributo t 
        WHERE t.produto_id = c.produto_id 
        AND t.cliente_id = c.cliente_id 
        AND t.tipo_operacao = '0'
    ) THEN 'entrada'
    ELSE 'saida'
END;
