# Plano de Reformulação do Sistema de Auditoria Fiscal

## Visão Geral

Este documento detalha as mudanças necessárias para reformular o sistema de Auditoria Fiscal, focando na criação de um novo modelo de cenários por tipo de tributo e na eliminação do sistema atual de status de tributos.

## Mudanças no Modelo de Dados

### 1. Novas Tabelas de Cenários

Criar tabelas específicas para cada tipo de tributo:

- `cenario_icms`
- `cenario_icms_st`
- `cenario_ipi`
- `cenario_pis`
- `cenario_cofins`
- `cenario_difal`

Cada tabela terá a seguinte estrutura base:

```sql
CREATE TABLE IF NOT EXISTS cenario_[tipo_tributo] (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,

    -- Campos específicos do tributo (variam conforme o tipo)
    -- Exemplo para ICMS:
    origem VARCHAR(2),
    cst VARCHAR(3),
    mod_bc VARCHAR(2), --TAG: modBC
    p_red_bc DECIMAL(10, 4), --TAG: pRedBC
    aliquota DECIMAL(10, 4), --TAG: pICMS
    p_dif DECIMAL(10, 4), --TAG: pDif

    -- Campos comuns a todos os cenários
    status VARCHAR(20) NOT NULL DEFAULT 'novo', -- 'novo', 'producao', 'inconsistente'
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT NOW(),
    data_atualizacao TIMESTAMP DEFAULT NOW(),

    -- Restrição para evitar cenários duplicados
    UNIQUE (empresa_id, cliente_id, produto_id, status)
);
```

### 2. Modificações na Tabela de Tributos

Adicionar colunas para armazenar os valores calculados com base nos cenários em produção e novas colunas necessárias:

```sql
-- Colunas para valores calculados
ALTER TABLE tributo ADD COLUMN icms_calculado_aliquota DECIMAL(10, 4);
ALTER TABLE tributo ADD COLUMN icms_calculado_valor DECIMAL(10, 2);
ALTER TABLE tributo ADD COLUMN icms_st_calculado_aliquota DECIMAL(10, 4);
ALTER TABLE tributo ADD COLUMN icms_st_calculado_valor DECIMAL(10, 2);
ALTER TABLE tributo ADD COLUMN ipi_calculado_aliquota DECIMAL(10, 4);
ALTER TABLE tributo ADD COLUMN ipi_calculado_valor DECIMAL(10, 2);
ALTER TABLE tributo ADD COLUMN pis_calculado_aliquota DECIMAL(10, 4);
ALTER TABLE tributo ADD COLUMN pis_calculado_valor DECIMAL(10, 2);
ALTER TABLE tributo ADD COLUMN cofins_calculado_aliquota DECIMAL(10, 4);
ALTER TABLE tributo ADD COLUMN cofins_calculado_valor DECIMAL(10, 2);
ALTER TABLE tributo ADD COLUMN difal_calculado_valor DECIMAL(10, 2);

-- Nova coluna para IPI EX
ALTER TABLE tributo ADD COLUMN ipi_ex VARCHAR(3);

-- Novas colunas para percentual de redução de base de cálculo para PIS e COFINS
ALTER TABLE tributo ADD COLUMN pis_p_red_bc DECIMAL(10, 4);
ALTER TABLE tributo ADD COLUMN cofins_p_red_bc DECIMAL(10, 4);
```

### 3. Modificações na Tabela de Clientes

Atualizar a coluna de status para refletir o preenchimento dos campos de atividade e destinação:

```sql
-- Valores possíveis: 'ok', 'sem_atividade', 'sem_destinacao', 'sem_ambos'
ALTER TABLE cliente ALTER COLUMN status TYPE VARCHAR(20);
```

### 4. Modificações na Tabela de Produtos

Simplificar o status do produto:

```sql
-- Valores possíveis: 'conforme', 'nulo'
ALTER TABLE produto ALTER COLUMN status TYPE VARCHAR(20);
```

Podemos remover as colunas de status específicos para cada tipo de tributo, uma vez que essas informações serão mantidas nos cenários.

## Mudanças no Backend

### 1. Novos Modelos

Criar modelos para cada tipo de cenário:

- `CenarioICMS`
- `CenarioICMSST`
- `CenarioIPI`
- `CenarioPIS`
- `CenarioCOFINS`
- `CenarioDIFAL`

### 2. Novas Rotas e Controladores

Criar rotas para gerenciar os cenários:

- `/api/cenarios/[tipo_tributo]` - Listar cenários
- `/api/cenarios/[tipo_tributo]/:id` - Obter, atualizar ou excluir cenário
- `/api/cenarios/[tipo_tributo]/status` - Atualizar status do cenário
- `/api/cenarios/[tipo_tributo]/vigencia` - Atualizar vigência do cenário
- `/api/cenarios/[tipo_tributo]/ativar` - Ativar/desativar cenário

### 3. Serviço de Importação XML

Modificar o serviço de importação para:

1. Verificar se já existe um cenário para a combinação empresa/cliente/produto/tributo
2. Se não existir, criar um novo cenário com status 'novo'
3. Se existir um cenário em produção, comparar os valores:
   - Se forem iguais, não criar novo cenário
   - Se forem diferentes, criar um novo cenário com status 'inconsistente'

### 4. Serviço de Cálculo de Tributos

Criar um serviço para calcular os valores de tributos com base nos cenários em produção:

1. Buscar o cenário ativo em produção para a combinação empresa/cliente/produto/tributo
2. Calcular o valor do tributo com base nas alíquotas do cenário
3. Atualizar as colunas de valores calculados na tabela de tributos
4. Atualizar o status do produto para 'conforme' se todos os cálculos forem realizados

## Mudanças no Frontend

### 1. Páginas de Cenários

Atualizar as páginas de cenários para:

1. Mostrar as abas 'Novos', 'Produção' e 'Inconsistentes'
2. Adicionar botões específicos para cada aba:
   - Novos: botão para atualizar status para 'producao'
   - Produção: botões para atualizar vigência e ativar/desativar
   - Inconsistentes: botões para excluir ou atualizar para 'producao'
3. Implementar modal de edição com abas para informações gerais, cliente, produto e tributo

### 2. Páginas de Detalhes de Cenários

Criar novas páginas para visualizar os detalhes de cada tipo de cenário, com:

1. Filtros específicos para cada tipo de tributo
2. Tabela com os cenários correspondentes
3. Modal para edição completa dos dados do cenário

### 3. Atualização da Interface de Clientes

Modificar a interface para:

1. Mostrar o novo status baseado no preenchimento de atividade e destinação
2. Adicionar filtros para os novos status

### 4. Atualização da Interface de Produtos

Simplificar a interface para mostrar apenas se o produto está 'conforme' ou 'nulo'.

## Fluxo de Criação e Atualização de Cenários

### Criação de Cenários

1. **Durante a importação XML**:

   - Para cada tributo no XML, verificar se já existe um cenário correspondente
   - Se não existir, criar um novo cenário com status 'novo'
   - Se existir um cenário em produção, comparar os valores:
     - Se forem iguais, não criar novo cenário
     - Se forem diferentes, criar um novo cenário com status 'inconsistente'

2. **Manualmente via interface**:
   - Usuário pode criar um novo cenário através do modal de edição
   - O cenário é criado com status 'novo'

### Atualização de Status

1. **De 'novo' para 'producao'**:

   - Usuário clica no botão de atualizar status
   - Sistema solicita data de início de vigência
   - Sistema atualiza o status para 'producao'

2. **De 'inconsistente' para 'producao'**:
   - Usuário clica no botão de atualizar status
   - Sistema solicita data de início de vigência
   - Sistema atualiza o status para 'producao'
   - Sistema atualiza a data de fim de vigência do cenário anterior em produção

### Ativação/Desativação

1. **Ativar cenário**:

   - Apenas cenários com status 'producao' podem ser ativados
   - Ao ativar um cenário, outros cenários do mesmo tipo para a mesma combinação empresa/cliente/produto são desativados

2. **Desativar cenário**:
   - Usuário pode desativar um cenário em produção
   - Cenários desativados não são usados para cálculos

## Considerações de Desempenho

1. **Relacionamentos por ID**:

   - O uso de relacionamentos por ID (empresa_id, cliente_id, produto_id) é a abordagem correta
   - Não afetará significativamente o desempenho se os índices apropriados forem criados
   - Permite que as informações sejam atualizadas em um único lugar e refletidas em todos os cenários

2. **Criação de Cenários na Importação**:
   - A criação de cenários durante a importação não deve impactar significativamente o desempenho
   - Implementar verificações eficientes para evitar cenários duplicados
   - Considerar processamento em lote para grandes volumes de importação

## Plano de Implementação

1. **Fase 1: Preparação do Banco de Dados**

   - Criar novas tabelas de cenários
   - Adicionar colunas de valores calculados à tabela de tributos
   - Atualizar definições de status nas tabelas existentes

2. **Fase 2: Implementação do Backend**

   - Criar novos modelos para cenários
   - Implementar novas rotas e controladores
   - Modificar serviço de importação XML
   - Criar serviço de cálculo de tributos

3. **Fase 3: Implementação do Frontend**

   - Atualizar páginas de cenários
   - Criar novas páginas de detalhes de cenários
   - Atualizar interfaces de clientes e produtos

4. **Fase 4: Testes e Ajustes**
   - Testar todas as funcionalidades
   - Ajustar desempenho conforme necessário
   - Corrigir problemas identificados

## Detalhamento das Tabelas de Cenários

### Cenário ICMS

```sql
CREATE TABLE IF NOT EXISTS cenario_icms (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,

    -- Campos específicos do ICMS
    origem VARCHAR(2),
    cst VARCHAR(3),
    mod_bc VARCHAR(2),
    p_red_bc DECIMAL(10, 4),
    aliquota DECIMAL(10, 4),
    p_dif DECIMAL(10, 4),

    -- Campos comuns
    status VARCHAR(20) NOT NULL DEFAULT 'novo', -- 'novo', 'producao', 'inconsistente'
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT NOW(),
    data_atualizacao TIMESTAMP DEFAULT NOW(),

    -- Restrição para evitar cenários duplicados
    UNIQUE (empresa_id, cliente_id, produto_id, status)
);

CREATE INDEX idx_cenario_icms_empresa ON cenario_icms(empresa_id);
CREATE INDEX idx_cenario_icms_cliente ON cenario_icms(cliente_id);
CREATE INDEX idx_cenario_icms_produto ON cenario_icms(produto_id);
CREATE INDEX idx_cenario_icms_status ON cenario_icms(status);
CREATE INDEX idx_cenario_icms_ativo ON cenario_icms(ativo);
```

### Cenário ICMS-ST

```sql
CREATE TABLE IF NOT EXISTS cenario_icms_st (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,

    -- Campos específicos do ICMS-ST
    -- Campos de ICMS que também são usados no ICMS-ST
    origem VARCHAR(2),
    cst VARCHAR(3),
    mod_bc VARCHAR(2),
    p_red_bc DECIMAL(10, 4),
    aliquota DECIMAL(10, 4),

    -- Campos específicos do ICMS-ST
    icms_st_mod_bc VARCHAR(2),
    icms_st_aliquota DECIMAL(10, 4),
    icms_st_p_mva DECIMAL(10, 4),

    -- Campos comuns
    status VARCHAR(20) NOT NULL DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT NOW(),
    data_atualizacao TIMESTAMP DEFAULT NOW(),

    UNIQUE (empresa_id, cliente_id, produto_id, status)
);

CREATE INDEX idx_cenario_icms_st_empresa ON cenario_icms_st(empresa_id);
CREATE INDEX idx_cenario_icms_st_cliente ON cenario_icms_st(cliente_id);
CREATE INDEX idx_cenario_icms_st_produto ON cenario_icms_st(produto_id);
CREATE INDEX idx_cenario_icms_st_status ON cenario_icms_st(status);
CREATE INDEX idx_cenario_icms_st_ativo ON cenario_icms_st(ativo);
```

### Cenário IPI

```sql
CREATE TABLE IF NOT EXISTS cenario_ipi (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,

    -- Campos específicos do IPI
    cst VARCHAR(3),
    aliquota DECIMAL(10, 4),
    ex VARCHAR(3),  -- Novo campo para EXTIPI

    -- Campos comuns
    status VARCHAR(20) NOT NULL DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT NOW(),
    data_atualizacao TIMESTAMP DEFAULT NOW(),

    -- Restrição de unicidade que não inclui o status 'producao'
    CONSTRAINT uq_cenario_ipi_unique EXCLUDE USING btree (
        empresa_id WITH =,
        cliente_id WITH =,
        produto_id WITH =,
        status WITH =
    ) WHERE (status != 'producao')
);

CREATE INDEX idx_cenario_ipi_empresa ON cenario_ipi(empresa_id);
CREATE INDEX idx_cenario_ipi_cliente ON cenario_ipi(cliente_id);
CREATE INDEX idx_cenario_ipi_produto ON cenario_ipi(produto_id);
CREATE INDEX idx_cenario_ipi_status ON cenario_ipi(status);
CREATE INDEX idx_cenario_ipi_ativo ON cenario_ipi(ativo);
```

### Cenário PIS

```sql
CREATE TABLE IF NOT EXISTS cenario_pis (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,

    -- Campos específicos do PIS
    cst VARCHAR(3),
    aliquota DECIMAL(10, 4),
    p_red_bc DECIMAL(10, 4),  -- Novo campo para percentual de redução da base de cálculo

    -- Campos comuns
    status VARCHAR(20) NOT NULL DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT NOW(),
    data_atualizacao TIMESTAMP DEFAULT NOW(),

    UNIQUE (empresa_id, cliente_id, produto_id, status)
);

CREATE INDEX idx_cenario_pis_empresa ON cenario_pis(empresa_id);
CREATE INDEX idx_cenario_pis_cliente ON cenario_pis(cliente_id);
CREATE INDEX idx_cenario_pis_produto ON cenario_pis(produto_id);
CREATE INDEX idx_cenario_pis_status ON cenario_pis(status);
CREATE INDEX idx_cenario_pis_ativo ON cenario_pis(ativo);
```

### Cenário COFINS

```sql
CREATE TABLE IF NOT EXISTS cenario_cofins (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,

    -- Campos específicos do COFINS
    cst VARCHAR(3),
    aliquota DECIMAL(10, 4),
    p_red_bc DECIMAL(10, 4),  -- Novo campo para percentual de redução da base de cálculo

    -- Campos comuns
    status VARCHAR(20) NOT NULL DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT NOW(),
    data_atualizacao TIMESTAMP DEFAULT NOW(),

    UNIQUE (empresa_id, cliente_id, produto_id, status)
);

CREATE INDEX idx_cenario_cofins_empresa ON cenario_cofins(empresa_id);
CREATE INDEX idx_cenario_cofins_cliente ON cenario_cofins(cliente_id);
CREATE INDEX idx_cenario_cofins_produto ON cenario_cofins(produto_id);
CREATE INDEX idx_cenario_cofins_status ON cenario_cofins(status);
CREATE INDEX idx_cenario_cofins_ativo ON cenario_cofins(ativo);
```

### Cenário DIFAL

```sql
CREATE TABLE IF NOT EXISTS cenario_difal (
    id SERIAL PRIMARY KEY,
    empresa_id INTEGER REFERENCES empresa(id) NOT NULL,
    escritorio_id INTEGER REFERENCES escritorio(id) NOT NULL,
    cliente_id INTEGER REFERENCES cliente(id) NOT NULL,
    produto_id INTEGER REFERENCES produto(id) NOT NULL,

    -- Campos de ICMS que também são usados no DIFAL
    origem VARCHAR(2),
    cst VARCHAR(3),
    mod_bc VARCHAR(2),
    p_red_bc DECIMAL(10, 4),
    aliquota DECIMAL(10, 4),

    -- Campos específicos do DIFAL
    p_fcp_uf_dest DECIMAL(10, 4),
    p_icms_uf_dest DECIMAL(10, 4),
    p_icms_inter DECIMAL(10, 4),
    p_icms_inter_part DECIMAL(10, 4),

    -- Campos comuns
    status VARCHAR(20) NOT NULL DEFAULT 'novo',
    data_inicio_vigencia DATE,
    data_fim_vigencia DATE,
    ativo BOOLEAN DEFAULT FALSE,
    data_criacao TIMESTAMP DEFAULT NOW(),
    data_atualizacao TIMESTAMP DEFAULT NOW(),

    UNIQUE (empresa_id, cliente_id, produto_id, status)
);

CREATE INDEX idx_cenario_difal_empresa ON cenario_difal(empresa_id);
CREATE INDEX idx_cenario_difal_cliente ON cenario_difal(cliente_id);
CREATE INDEX idx_cenario_difal_produto ON cenario_difal(produto_id);
CREATE INDEX idx_cenario_difal_status ON cenario_difal(status);
CREATE INDEX idx_cenario_difal_ativo ON cenario_difal(ativo);
```

## Detalhamento dos Modelos Python

### Modelo Base para Cenários

```python
class CenarioBase(db.Model):
    """Classe base abstrata para todos os modelos de cenário"""
    __abstract__ = True

    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.Integer, db.ForeignKey('empresa.id'), nullable=False)
    escritorio_id = db.Column(db.Integer, db.ForeignKey('escritorio.id'), nullable=False)
    cliente_id = db.Column(db.Integer, db.ForeignKey('cliente.id'), nullable=False)
    produto_id = db.Column(db.Integer, db.ForeignKey('produto.id'), nullable=False)

    status = db.Column(db.String(20), default='novo')  # 'novo', 'producao', 'inconsistente'
    data_inicio_vigencia = db.Column(db.Date)
    data_fim_vigencia = db.Column(db.Date)
    ativo = db.Column(db.Boolean, default=False)
    data_criacao = db.Column(db.DateTime, server_default=func.now())
    data_atualizacao = db.Column(db.DateTime, server_default=func.now(), onupdate=func.now())

    # Relacionamentos
    empresa = db.relationship('Empresa', backref='cenarios')
    escritorio = db.relationship('Escritorio', backref='cenarios')
    cliente = db.relationship('Cliente', backref='cenarios')
    produto = db.relationship('Produto', backref='cenarios')

    def to_dict(self):
        """Converte o modelo para um dicionário"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'cliente_id': self.cliente_id,
            'produto_id': self.produto_id,
            'status': self.status,
            'data_inicio_vigencia': self.data_inicio_vigencia.isoformat() if self.data_inicio_vigencia else None,
            'data_fim_vigencia': self.data_fim_vigencia.isoformat() if self.data_fim_vigencia else None,
            'ativo': self.ativo,
            'data_criacao': self.data_criacao.isoformat() if self.data_criacao else None,
            'data_atualizacao': self.data_atualizacao.isoformat() if self.data_atualizacao else None
        }
```

### Modelo para Cenário ICMS

```python
class CenarioICMS(CenarioBase):
    __tablename__ = 'cenario_icms'

    # Campos específicos do ICMS
    origem = db.Column(db.String(2))
    cst = db.Column(db.String(3))
    mod_bc = db.Column(db.String(2))
    p_red_bc = db.Column(db.Numeric(10, 4))
    aliquota = db.Column(db.Numeric(10, 4))
    p_dif = db.Column(db.Numeric(10, 4))

    def __repr__(self):
        return f"<CenarioICMS {self.id} - Produto {self.produto_id} - Cliente {self.cliente_id}>"

    def to_dict(self):
        """Converte o modelo para um dicionário"""
        base_dict = super().to_dict()
        icms_dict = {
            'origem': self.origem,
            'cst': self.cst,
            'mod_bc': self.mod_bc,
            'p_red_bc': float(self.p_red_bc) if self.p_red_bc else None,
            'aliquota': float(self.aliquota) if self.aliquota else None,
            'p_dif': float(self.p_dif) if self.p_dif else None
        }
        return {**base_dict, **icms_dict}
```

### Modelo para Cenário ICMS-ST

```python
class CenarioICMSST(CenarioBase):
    __tablename__ = 'cenario_icms_st'

    # Campos de ICMS que também são usados no ICMS-ST
    origem = db.Column(db.String(2))
    cst = db.Column(db.String(3))
    mod_bc = db.Column(db.String(2))
    p_red_bc = db.Column(db.Numeric(10, 4))
    aliquota = db.Column(db.Numeric(10, 4))

    # Campos específicos do ICMS-ST
    icms_st_mod_bc = db.Column(db.String(2))
    icms_st_aliquota = db.Column(db.Numeric(10, 4))
    icms_st_p_mva = db.Column(db.Numeric(10, 4))

    def __repr__(self):
        return f"<CenarioICMSST {self.id} - Produto {self.produto_id} - Cliente {self.cliente_id}>"

    def to_dict(self):
        """Converte o modelo para um dicionário"""
        base_dict = super().to_dict()
        icms_st_dict = {
            'origem': self.origem,
            'cst': self.cst,
            'mod_bc': self.mod_bc,
            'p_red_bc': float(self.p_red_bc) if self.p_red_bc else None,
            'aliquota': float(self.aliquota) if self.aliquota else None,
            'icms_st_mod_bc': self.icms_st_mod_bc,
            'icms_st_aliquota': float(self.icms_st_aliquota) if self.icms_st_aliquota else None,
            'icms_st_p_mva': float(self.icms_st_p_mva) if self.icms_st_p_mva else None
        }
        return {**base_dict, **icms_st_dict}
```

### Modelo para Cenário IPI

```python
class CenarioIPI(CenarioBase):
    __tablename__ = 'cenario_ipi'

    # Campos específicos do IPI
    cst = db.Column(db.String(3))
    aliquota = db.Column(db.Numeric(10, 4))
    ex = db.Column(db.String(3))  # Campo para EXTIPI

    def __repr__(self):
        return f"<CenarioIPI {self.id} - Produto {self.produto_id} - Cliente {self.cliente_id}>"

    def to_dict(self):
        """Converte o modelo para um dicionário"""
        base_dict = super().to_dict()
        ipi_dict = {
            'cst': self.cst,
            'aliquota': float(self.aliquota) if self.aliquota else None,
            'ex': self.ex
        }
        return {**base_dict, **ipi_dict}
```

### Modelo para Cenário PIS

```python
class CenarioPIS(CenarioBase):
    __tablename__ = 'cenario_pis'

    # Campos específicos do PIS
    cst = db.Column(db.String(3))
    aliquota = db.Column(db.Numeric(10, 4))
    p_red_bc = db.Column(db.Numeric(10, 4))  # Percentual de redução da base de cálculo

    def __repr__(self):
        return f"<CenarioPIS {self.id} - Produto {self.produto_id} - Cliente {self.cliente_id}>"

    def to_dict(self):
        """Converte o modelo para um dicionário"""
        base_dict = super().to_dict()
        pis_dict = {
            'cst': self.cst,
            'aliquota': float(self.aliquota) if self.aliquota else None,
            'p_red_bc': float(self.p_red_bc) if self.p_red_bc else None
        }
        return {**base_dict, **pis_dict}
```

### Modelo para Cenário COFINS

```python
class CenarioCOFINS(CenarioBase):
    __tablename__ = 'cenario_cofins'

    # Campos específicos do COFINS
    cst = db.Column(db.String(3))
    aliquota = db.Column(db.Numeric(10, 4))
    p_red_bc = db.Column(db.Numeric(10, 4))  # Percentual de redução da base de cálculo

    def __repr__(self):
        return f"<CenarioCOFINS {self.id} - Produto {self.produto_id} - Cliente {self.cliente_id}>"

    def to_dict(self):
        """Converte o modelo para um dicionário"""
        base_dict = super().to_dict()
        cofins_dict = {
            'cst': self.cst,
            'aliquota': float(self.aliquota) if self.aliquota else None,
            'p_red_bc': float(self.p_red_bc) if self.p_red_bc else None
        }
        return {**base_dict, **cofins_dict}
```

### Modelo para Cenário DIFAL

```python
class CenarioDIFAL(CenarioBase):
    __tablename__ = 'cenario_difal'

    # Campos de ICMS que também são usados no DIFAL
    origem = db.Column(db.String(2))
    cst = db.Column(db.String(3))
    mod_bc = db.Column(db.String(2))
    p_red_bc = db.Column(db.Numeric(10, 4))
    aliquota = db.Column(db.Numeric(10, 4))

    # Campos específicos do DIFAL
    p_fcp_uf_dest = db.Column(db.Numeric(10, 4))
    p_icms_uf_dest = db.Column(db.Numeric(10, 4))
    p_icms_inter = db.Column(db.Numeric(10, 4))
    p_icms_inter_part = db.Column(db.Numeric(10, 4))

    def __repr__(self):
        return f"<CenarioDIFAL {self.id} - Produto {self.produto_id} - Cliente {self.cliente_id}>"

    def to_dict(self):
        """Converte o modelo para um dicionário"""
        base_dict = super().to_dict()
        difal_dict = {
            'origem': self.origem,
            'cst': self.cst,
            'mod_bc': self.mod_bc,
            'p_red_bc': float(self.p_red_bc) if self.p_red_bc else None,
            'aliquota': float(self.aliquota) if self.aliquota else None,
            'p_fcp_uf_dest': float(self.p_fcp_uf_dest) if self.p_fcp_uf_dest else None,
            'p_icms_uf_dest': float(self.p_icms_uf_dest) if self.p_icms_uf_dest else None,
            'p_icms_inter': float(self.p_icms_inter) if self.p_icms_inter else None,
            'p_icms_inter_part': float(self.p_icms_inter_part) if self.p_icms_inter_part else None
        }
        return {**base_dict, **difal_dict}
```

## Detalhamento das Rotas e Controladores

### Blueprint para Cenários

```python
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, Usuario, CenarioICMS, CenarioICMSST, CenarioIPI, CenarioPIS, CenarioCOFINS, CenarioDIFAL
from datetime import datetime

cenario_bp = Blueprint('cenario_bp', __name__)

# Mapeamento de tipos de tributo para modelos
CENARIO_MODELS = {
    'icms': CenarioICMS,
    'icms_st': CenarioICMSST,
    'ipi': CenarioIPI,
    'pis': CenarioPIS,
    'cofins': CenarioCOFINS,
    'difal': CenarioDIFAL
}

@cenario_bp.route('/api/cenarios/<tipo_tributo>', methods=['GET'])
@jwt_required()
def listar_cenarios(tipo_tributo):
    """
    Lista os cenários de um tipo específico com base nas permissões do usuário
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in CENARIO_MODELS:
            return jsonify({"message": f"Tipo de tributo {tipo_tributo} inválido"}), 400

        # Obter modelo correspondente
        CenarioModel = CENARIO_MODELS[tipo_tributo]

        # Obter parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)
        status = request.args.get('status')  # novo, producao, inconsistente
        cliente_id = request.args.get('cliente_id', type=int)
        produto_id = request.args.get('produto_id', type=int)
        ativo = request.args.get('ativo', type=bool)

        # Verificar permissões do usuário
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Construir query base
        query = CenarioModel.query

        # Aplicar filtros
        if empresa_id:
            query = query.filter_by(empresa_id=empresa_id)
        if status:
            query = query.filter_by(status=status)
        if cliente_id:
            query = query.filter_by(cliente_id=cliente_id)
        if produto_id:
            query = query.filter_by(produto_id=produto_id)
        if ativo is not None:
            query = query.filter_by(ativo=ativo)

        # Aplicar filtros com base no tipo de usuário
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores veem todos os cenários
            pass
        elif usuario.tipo_usuario == 'escritorio':
            # Usuários do tipo escritório veem cenários do seu escritório
            query = query.filter_by(escritorio_id=usuario.escritorio_id)
        else:
            # Usuários comuns veem apenas cenários das empresas permitidas
            empresas_permitidas = usuario.empresas_permitidas or []
            query = query.filter(CenarioModel.empresa_id.in_(empresas_permitidas))

        # Executar a query
        cenarios = query.all()

        return jsonify({
            "cenarios": [cenario.to_dict() for cenario in cenarios]
        }), 200

    except Exception as e:
        print(f"Erro ao listar cenários: {str(e)}")
        return jsonify({"message": f"Erro ao processar solicitação: {str(e)}"}), 500
```

### Rota para Atualizar Status do Cenário

```python
@cenario_bp.route('/api/cenarios/<tipo_tributo>/<int:cenario_id>/status', methods=['PUT'])
@jwt_required()
def atualizar_status_cenario(tipo_tributo, cenario_id):
    """
    Atualiza o status de um cenário específico
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in CENARIO_MODELS:
            return jsonify({"message": f"Tipo de tributo {tipo_tributo} inválido"}), 400

        # Obter modelo correspondente
        CenarioModel = CENARIO_MODELS[tipo_tributo]

        # Obter dados da requisição
        data = request.get_json()
        novo_status = data.get('status')  # novo, producao, inconsistente
        data_inicio_vigencia = data.get('data_inicio_vigencia')

        if not novo_status:
            return jsonify({"message": "Status é obrigatório"}), 400

        if novo_status == 'producao' and not data_inicio_vigencia:
            return jsonify({"message": "Data de início de vigência é obrigatória para status 'producao'"}), 400

        # Verificar permissões do usuário
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar o cenário
        cenario = db.session.get(CenarioModel, cenario_id)

        if not cenario:
            return jsonify({"message": f"Cenário {tipo_tributo} não encontrado"}), 404

        # Se o status for o mesmo, não fazer nada
        if cenario.status == novo_status:
            return jsonify({
                "message": f"Status do cenário {tipo_tributo} já é {novo_status}",
                "cenario": cenario.to_dict()
            }), 200

        # Se estiver atualizando para 'producao', verificar se já existe outro cenário em produção
        if novo_status == 'producao':
            # Converter string para data
            data_inicio = datetime.strptime(data_inicio_vigencia, '%Y-%m-%d').date()

            # Buscar cenário em produção existente
            cenario_producao = CenarioModel.query.filter_by(
                empresa_id=cenario.empresa_id,
                cliente_id=cenario.cliente_id,
                produto_id=cenario.produto_id,
                status='producao'
            ).first()

            if cenario_producao:
                # Atualizar a data de fim de vigência do cenário anterior
                cenario_producao.data_fim_vigencia = data_inicio
                cenario_producao.ativo = False

        # Atualizar o status do cenário
        cenario.status = novo_status

        # Se for 'producao', atualizar data de início de vigência
        if novo_status == 'producao':
            cenario.data_inicio_vigencia = data_inicio

            # Se não houver outro cenário em produção, ativar este
            if not cenario_producao:
                cenario.ativo = True

        # Salvar alterações
        db.session.commit()

        return jsonify({
            "message": f"Status do cenário {tipo_tributo} atualizado para {novo_status}",
            "cenario": cenario.to_dict()
        }), 200

    except Exception as e:
        print(f"Erro ao atualizar status do cenário: {str(e)}")
        db.session.rollback()
        return jsonify({"message": f"Erro ao processar solicitação: {str(e)}"}), 500
```

## Detalhamento do Serviço de Importação XML

### Modificações no XMLImportService

```python
class XMLImportService:
    """
    Serviço para importação de arquivos XML de notas fiscais
    """

    def __init__(self, empresa_id, escritorio_id, usuario_id):
        self.empresa_id = empresa_id
        self.escritorio_id = escritorio_id
        self.usuario_id = usuario_id

    def _process_tributo_and_cenarios(self, produto_data, cliente_id, produto_id, info_nfe):
        """
        Processa os tributos do produto e cria/atualiza cenários

        Args:
            produto_data (dict): Dados do produto com tributos
            cliente_id (int): ID do cliente
            produto_id (int): ID do produto
            info_nfe (dict): Informações da NFe

        Returns:
            tuple: (Tributo, dict de cenários criados/atualizados)
        """
        # Processar o tributo normalmente (código existente)
        tributo = self._process_tributo(produto_data, cliente_id, produto_id, info_nfe)

        # Dicionário para armazenar cenários criados/atualizados
        cenarios_processados = {}

        # Processar cenários para cada tipo de tributo
        tipos_tributo = ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal']

        for tipo in tipos_tributo:
            # Verificar se o tributo tem valores para este tipo
            if self._has_tributo_values(tributo, tipo):
                # Processar cenário para este tipo de tributo
                cenario = self._process_cenario(tributo, tipo)
                cenarios_processados[tipo] = cenario

        return tributo, cenarios_processados

    def _has_tributo_values(self, tributo, tipo_tributo):
        """
        Verifica se o tributo tem valores para um tipo específico

        Args:
            tributo (Tributo): Objeto do tributo
            tipo_tributo (str): Tipo de tributo

        Returns:
            bool: True se o tributo tem valores para o tipo, False caso contrário
        """
        if tipo_tributo == 'icms':
            return tributo.icms_valor is not None
        elif tipo_tributo == 'icms_st':
            return tributo.icms_st_valor is not None
        elif tipo_tributo == 'ipi':
            return tributo.ipi_valor is not None
        elif tipo_tributo == 'pis':
            return tributo.pis_valor is not None
        elif tipo_tributo == 'cofins':
            return tributo.cofins_valor is not None
        elif tipo_tributo == 'difal':
            return tributo.difal_v_icms_uf_dest is not None
        return False

    def _process_cenario(self, tributo, tipo_tributo):
        """
        Processa um cenário para um tipo específico de tributo

        Args:
            tributo (Tributo): Objeto do tributo
            tipo_tributo (str): Tipo de tributo

        Returns:
            object: Objeto do cenário criado/atualizado
        """
        # Obter o modelo correspondente
        CenarioModel = {
            'icms': CenarioICMS,
            'icms_st': CenarioICMSST,
            'ipi': CenarioIPI,
            'pis': CenarioPIS,
            'cofins': CenarioCOFINS,
            'difal': CenarioDIFAL
        }[tipo_tributo]

        # Verificar se já existe um cenário em produção para esta combinação
        cenario_producao = CenarioModel.query.filter_by(
            empresa_id=tributo.empresa_id,
            cliente_id=tributo.cliente_id,
            produto_id=tributo.produto_id,
            status='producao',
            ativo=True
        ).first()

        # Extrair valores do tributo para o tipo específico
        valores_tributo = self._extract_tributo_values(tributo, tipo_tributo)

        # Se existe um cenário em produção, comparar os valores
        if cenario_producao:
            # Extrair valores do cenário em produção
            valores_cenario = self._extract_cenario_values(cenario_producao, tipo_tributo)

            # Comparar valores
            if self._compare_values(valores_tributo, valores_cenario):
                # Valores são iguais, não criar novo cenário
                return cenario_producao
            else:
                # Valores são diferentes, criar cenário inconsistente
                return self._create_cenario(tributo, tipo_tributo, valores_tributo, 'inconsistente')
        else:
            # Não existe cenário em produção, criar cenário novo
            return self._create_cenario(tributo, tipo_tributo, valores_tributo, 'novo')

    def _extract_tributo_values(self, tributo, tipo_tributo):
        """
        Extrai os valores de um tributo para um tipo específico

        Args:
            tributo (Tributo): Objeto do tributo
            tipo_tributo (str): Tipo de tributo

        Returns:
            dict: Dicionário com os valores do tributo
        """
        if tipo_tributo == 'icms':
            return {
                'origem': tributo.icms_origem,
                'cst': tributo.icms_cst,
                'mod_bc': tributo.icms_mod_bc,
                'p_red_bc': tributo.icms_p_red_bc,
                'aliquota': tributo.icms_aliquota,
                'p_dif': tributo.icms_p_dif
            }
        elif tipo_tributo == 'icms_st':
            return {
                'origem': tributo.icms_origem,
                'cst': tributo.icms_cst,
                'mod_bc': tributo.icms_mod_bc,
                'p_red_bc': tributo.icms_p_red_bc,
                'aliquota': tributo.icms_aliquota,
                'icms_st_mod_bc': tributo.icms_st_mod_bc,
                'icms_st_aliquota': tributo.icms_st_aliquota,
                'icms_st_p_mva': tributo.icms_st_p_mva
            }
        elif tipo_tributo == 'ipi':
            return {
                'cst': tributo.ipi_cst,
                'aliquota': tributo.ipi_aliquota,
                'ex': tributo.ipi_ex  # Novo campo para EXTIPI
            }
        elif tipo_tributo == 'pis':
            return {
                'cst': tributo.pis_cst,
                'aliquota': tributo.pis_aliquota,
                'p_red_bc': tributo.pis_p_red_bc  # Novo campo para percentual de redução da base de cálculo
            }
        elif tipo_tributo == 'cofins':
            return {
                'cst': tributo.cofins_cst,
                'aliquota': tributo.cofins_aliquota,
                'p_red_bc': tributo.cofins_p_red_bc  # Novo campo para percentual de redução da base de cálculo
            }
        elif tipo_tributo == 'difal':
            return {
                'origem': tributo.icms_origem,
                'cst': tributo.icms_cst,
                'mod_bc': tributo.icms_mod_bc,
                'p_red_bc': tributo.icms_p_red_bc,
                'aliquota': tributo.icms_aliquota,
                'p_fcp_uf_dest': tributo.difal_p_fcp_uf_dest,
                'p_icms_uf_dest': tributo.difal_p_icms_uf_dest,
                'p_icms_inter': tributo.difal_p_icms_inter,
                'p_icms_inter_part': tributo.difal_p_icms_inter_part
            }
        return {}

    def _create_cenario(self, tributo, tipo_tributo, valores, status):
        """
        Cria um novo cenário

        Args:
            tributo (Tributo): Objeto do tributo
            tipo_tributo (str): Tipo de tributo
            valores (dict): Valores do tributo
            status (str): Status do cenário ('novo', 'producao', 'inconsistente')

        Returns:
            object: Objeto do cenário criado
        """
        # Obter o modelo correspondente
        CenarioModel = {
            'icms': CenarioICMS,
            'icms_st': CenarioICMSST,
            'ipi': CenarioIPI,
            'pis': CenarioPIS,
            'cofins': CenarioCOFINS,
            'difal': CenarioDIFAL
        }[tipo_tributo]

        # Criar o cenário com os valores do tributo
        cenario = CenarioModel(
            empresa_id=tributo.empresa_id,
            escritorio_id=tributo.escritorio_id,
            cliente_id=tributo.cliente_id,
            produto_id=tributo.produto_id,
            status=status,
            **valores
        )

        db.session.add(cenario)
        db.session.flush()  # Obter o ID sem commit

        return cenario
```

## Serviço de Cálculo de Tributos

```python
class TributoCalculationService:
    """
    Serviço para cálculo de tributos com base nos cenários em produção
    """

    def __init__(self, empresa_id):
        self.empresa_id = empresa_id

    def calculate_tributos(self, tributo_ids=None, produto_ids=None):
        """
        Calcula os valores de tributos com base nos cenários em produção

        Args:
            tributo_ids (list): Lista de IDs de tributos para calcular (opcional)
            produto_ids (list): Lista de IDs de produtos para calcular (opcional)

        Returns:
            dict: Resultado do cálculo
        """
        # Construir query base
        query = Tributo.query.filter_by(empresa_id=self.empresa_id)

        # Aplicar filtros
        if tributo_ids:
            query = query.filter(Tributo.id.in_(tributo_ids))
        if produto_ids:
            query = query.filter(Tributo.produto_id.in_(produto_ids))

        # Executar a query
        tributos = query.all()

        # Resultados
        results = {
            'total': len(tributos),
            'calculados': 0,
            'nao_calculados': 0,
            'por_tipo': {
                'icms': {'calculados': 0, 'nao_calculados': 0},
                'icms_st': {'calculados': 0, 'nao_calculados': 0},
                'ipi': {'calculados': 0, 'nao_calculados': 0},
                'pis': {'calculados': 0, 'nao_calculados': 0},
                'cofins': {'calculados': 0, 'nao_calculados': 0},
                'difal': {'calculados': 0, 'nao_calculados': 0}
            }
        }

        # Processar cada tributo
        for tributo in tributos:
            # Calcular para cada tipo de tributo
            tipos_tributo = ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal']
            all_calculated = True

            for tipo in tipos_tributo:
                # Verificar se o tributo tem valores para este tipo
                if self._has_tributo_values(tributo, tipo):
                    # Calcular valores com base no cenário em produção
                    calculated = self._calculate_tributo_values(tributo, tipo)

                    if calculated:
                        results['por_tipo'][tipo]['calculados'] += 1
                    else:
                        results['por_tipo'][tipo]['nao_calculados'] += 1
                        all_calculated = False

            # Atualizar contadores
            if all_calculated:
                results['calculados'] += 1
                # Atualizar status do produto para 'conforme'
                produto = db.session.get(Produto, tributo.produto_id)
                if produto:
                    produto.status = 'conforme'
            else:
                results['nao_calculados'] += 1

        # Salvar alterações
        db.session.commit()

        return results

    def _has_tributo_values(self, tributo, tipo_tributo):
        """
        Verifica se o tributo tem valores para um tipo específico

        Args:
            tributo (Tributo): Objeto do tributo
            tipo_tributo (str): Tipo de tributo

        Returns:
            bool: True se o tributo tem valores para o tipo, False caso contrário
        """
        # Implementação similar à do XMLImportService
        pass

    def _calculate_tributo_values(self, tributo, tipo_tributo):
        """
        Calcula os valores de um tributo com base no cenário em produção

        Args:
            tributo (Tributo): Objeto do tributo
            tipo_tributo (str): Tipo de tributo

        Returns:
            bool: True se o cálculo foi realizado, False caso contrário
        """
        # Obter o modelo correspondente
        CenarioModel = {
            'icms': CenarioICMS,
            'icms_st': CenarioICMSST,
            'ipi': CenarioIPI,
            'pis': CenarioPIS,
            'cofins': CenarioCOFINS,
            'difal': CenarioDIFAL
        }[tipo_tributo]

        # Buscar cenário em produção ativo
        cenario = CenarioModel.query.filter_by(
            empresa_id=tributo.empresa_id,
            cliente_id=tributo.cliente_id,
            produto_id=tributo.produto_id,
            status='producao',
            ativo=True
        ).first()

        # Se não encontrar cenário ativo, verificar se existe algum em produção
        if not cenario:
            cenario = CenarioModel.query.filter_by(
                empresa_id=tributo.empresa_id,
                cliente_id=tributo.cliente_id,
                produto_id=tributo.produto_id,
                status='producao'
            ).first()

        # Se não encontrar nenhum cenário em produção, não calcular
        if not cenario:
            return False

        # Calcular valores com base no cenário
        if tipo_tributo == 'icms':
            # Calcular ICMS
            if cenario.aliquota is not None and tributo.valor_total is not None:
                # Aplicar redução da base de cálculo, se houver
                base_calculo = float(tributo.valor_total)
                if cenario.p_red_bc is not None and float(cenario.p_red_bc) > 0:
                    base_calculo = base_calculo * (1 - float(cenario.p_red_bc) / 100)

                # Calcular valor do ICMS
                valor_calculado = (base_calculo * float(cenario.aliquota)) / 100

                # Atualizar valores calculados no tributo
                tributo.icms_calculado_aliquota = cenario.aliquota
                tributo.icms_calculado_valor = valor_calculado

                return True

        elif tipo_tributo == 'icms_st':
            # Calcular ICMS-ST
            if cenario.icms_st_aliquota is not None and tributo.valor_total is not None:
                # Aplicar redução da base de cálculo, se houver
                base_calculo = float(tributo.valor_total)
                if cenario.p_red_bc is not None and float(cenario.p_red_bc) > 0:
                    base_calculo = base_calculo * (1 - float(cenario.p_red_bc) / 100)

                # Aplicar MVA, se houver
                if cenario.icms_st_p_mva is not None and float(cenario.icms_st_p_mva) > 0:
                    base_calculo = base_calculo * (1 + float(cenario.icms_st_p_mva) / 100)

                # Calcular valor do ICMS-ST
                valor_calculado = (base_calculo * float(cenario.icms_st_aliquota)) / 100

                # Subtrair o ICMS próprio, se houver
                if tributo.icms_calculado_valor is not None:
                    valor_calculado -= float(tributo.icms_calculado_valor)

                # Atualizar valores calculados no tributo
                tributo.icms_st_calculado_aliquota = cenario.icms_st_aliquota
                tributo.icms_st_calculado_valor = valor_calculado

                return True

        elif tipo_tributo == 'ipi':
            # Calcular IPI
            if cenario.aliquota is not None and tributo.valor_total is not None:
                # Calcular valor do IPI
                valor_calculado = (float(tributo.valor_total) * float(cenario.aliquota)) / 100

                # Atualizar valores calculados no tributo
                tributo.ipi_calculado_aliquota = cenario.aliquota
                tributo.ipi_calculado_valor = valor_calculado

                return True

        elif tipo_tributo == 'pis':
            # Calcular PIS
            if cenario.aliquota is not None and tributo.valor_total is not None:
                # Aplicar redução da base de cálculo, se houver
                base_calculo = float(tributo.valor_total)
                if cenario.p_red_bc is not None and float(cenario.p_red_bc) > 0:
                    base_calculo = base_calculo * (1 - float(cenario.p_red_bc) / 100)

                # Calcular valor do PIS
                valor_calculado = (base_calculo * float(cenario.aliquota)) / 100

                # Atualizar valores calculados no tributo
                tributo.pis_calculado_aliquota = cenario.aliquota
                tributo.pis_calculado_valor = valor_calculado

                return True

        elif tipo_tributo == 'cofins':
            # Calcular COFINS
            if cenario.aliquota is not None and tributo.valor_total is not None:
                # Aplicar redução da base de cálculo, se houver
                base_calculo = float(tributo.valor_total)
                if cenario.p_red_bc is not None and float(cenario.p_red_bc) > 0:
                    base_calculo = base_calculo * (1 - float(cenario.p_red_bc) / 100)

                # Calcular valor do COFINS
                valor_calculado = (base_calculo * float(cenario.aliquota)) / 100

                # Atualizar valores calculados no tributo
                tributo.cofins_calculado_aliquota = cenario.aliquota
                tributo.cofins_calculado_valor = valor_calculado

                return True

        elif tipo_tributo == 'difal':
            # Calcular DIFAL
            if (cenario.p_icms_uf_dest is not None and
                cenario.p_icms_inter is not None and
                tributo.valor_total is not None):

                # Aplicar redução da base de cálculo, se houver
                base_calculo = float(tributo.valor_total)
                if cenario.p_red_bc is not None and float(cenario.p_red_bc) > 0:
                    base_calculo = base_calculo * (1 - float(cenario.p_red_bc) / 100)

                # Calcular diferença entre alíquotas
                diferenca = float(cenario.p_icms_uf_dest) - float(cenario.p_icms_inter)

                # Aplicar percentual de partilha
                if cenario.p_icms_inter_part is not None:
                    diferenca = diferenca * float(cenario.p_icms_inter_part) / 100

                # Calcular valor do DIFAL
                valor_calculado = (base_calculo * diferenca) / 100

                # Calcular FCP, se houver
                if cenario.p_fcp_uf_dest is not None and float(cenario.p_fcp_uf_dest) > 0:
                    fcp = (base_calculo * float(cenario.p_fcp_uf_dest)) / 100
                    valor_calculado += fcp

                # Atualizar valor calculado no tributo
                tributo.difal_calculado_valor = valor_calculado

                return True

        return False
```

## Atualização do Processo de Importação XML

Para processar os novos campos durante a importação XML, precisamos atualizar o método `_process_tributo` no serviço de importação:

```python
def _process_tributo(self, produto_data, cliente_id, produto_id, info_nfe):
    """
    Processa os tributos do produto

    Args:
        produto_data (dict): Dados do produto com tributos
        cliente_id (int): ID do cliente
        produto_id (int): ID do produto
        info_nfe (dict): Informações da NFe

    Returns:
        Tributo: Objeto do tributo
    """
    # Verificar se já existe um tributo para este produto/cliente/data/NF
    tributo = Tributo.query.filter_by(
        empresa_id=self.empresa_id,
        cliente_id=cliente_id,
        produto_id=produto_id,
        data_emissao=info_nfe.get('data_emissao'),
        numero_nf=info_nfe.get('numero')
    ).first()

    # Se não existir, criar um novo tributo
    if not tributo:
        # Criar novo tributo
        tributo = Tributo(
            empresa_id=self.empresa_id,
            escritorio_id=self.escritorio_id,
            cliente_id=cliente_id,
            produto_id=produto_id,
            data_emissao=info_nfe.get('data_emissao'),
            data_saida=info_nfe.get('data_saida'),
            numero_nf=info_nfe.get('numero'),
            chave_nf=info_nfe.get('chave'),
            tipo_operacao=info_nfe.get('tipo_operacao'),  # 0=entrada, 1=saída
            status='novo'
        )
        db.session.add(tributo)
        logger.info(f"Criado novo tributo para produto_id={produto_id}, cliente_id={cliente_id}, NF={info_nfe.get('numero')}")

    # Atualizar informações de tributos
    # ICMS
    tributo.icms_origem = produto_data.get('icms_origem')
    tributo.icms_cst = produto_data.get('icms_cst')
    tributo.icms_mod_bc = produto_data.get('icms_mod_bc')
    tributo.icms_p_red_bc = produto_data.get('icms_p_red_bc')
    tributo.icms_vbc = produto_data.get('icms_vbc')
    tributo.icms_aliquota = produto_data.get('icms_aliquota')
    tributo.icms_valor = produto_data.get('icms_valor')
    tributo.icms_v_op = produto_data.get('icms_v_op')
    tributo.icms_p_dif = produto_data.get('icms_p_dif')
    tributo.icms_v_dif = produto_data.get('icms_v_dif')

    # ICMS-ST
    tributo.icms_st_mod_bc = produto_data.get('icms_st_mod_bc')
    tributo.icms_st_p_mva = produto_data.get('icms_st_p_mva')
    tributo.icms_st_vbc = produto_data.get('icms_st_vbc')
    tributo.icms_st_aliquota = produto_data.get('icms_st_aliquota')
    tributo.icms_st_valor = produto_data.get('icms_st_valor')

    # IPI
    tributo.ipi_cst = produto_data.get('ipi_cst')
    tributo.ipi_vbc = produto_data.get('ipi_vbc')
    tributo.ipi_aliquota = produto_data.get('ipi_aliquota')
    tributo.ipi_valor = produto_data.get('ipi_valor')
    tributo.ipi_codigo_enquadramento = produto_data.get('ipi_codigo_enquadramento')
    tributo.ipi_ex = produto_data.get('ipi_ex')  # Novo campo para EXTIPI

    # PIS
    tributo.pis_cst = produto_data.get('pis_cst')
    tributo.pis_vbc = produto_data.get('pis_vbc')
    tributo.pis_aliquota = produto_data.get('pis_aliquota')
    tributo.pis_valor = produto_data.get('pis_valor')
    # O campo pis_p_red_bc não é preenchido na importação, apenas no cenário

    # COFINS
    tributo.cofins_cst = produto_data.get('cofins_cst')
    tributo.cofins_vbc = produto_data.get('cofins_vbc')
    tributo.cofins_aliquota = produto_data.get('cofins_aliquota')
    tributo.cofins_valor = produto_data.get('cofins_valor')
    # O campo cofins_p_red_bc não é preenchido na importação, apenas no cenário

    # DIFAL
    tributo.difal_vbc = produto_data.get('difal_vbc')
    tributo.difal_p_fcp_uf_dest = produto_data.get('difal_p_fcp_uf_dest')
    tributo.difal_p_icms_uf_dest = produto_data.get('difal_p_icms_uf_dest')
    tributo.difal_p_icms_inter = produto_data.get('difal_p_icms_inter')
    tributo.difal_p_icms_inter_part = produto_data.get('difal_p_icms_inter_part')
    tributo.difal_v_fcp_uf_dest = produto_data.get('difal_v_fcp_uf_dest')
    tributo.difal_v_icms_uf_dest = produto_data.get('difal_v_icms_uf_dest')
    tributo.difal_v_icms_uf_remet = produto_data.get('difal_v_icms_uf_remet')

    # Valores do produto
    tributo.quantidade = produto_data.get('quantidade')
    tributo.valor_unitario = produto_data.get('valor_unitario')
    tributo.valor_total = produto_data.get('valor_total')

    return tributo
```

Após processar o tributo, o método `_process_tributo_and_cenarios` criará os cenários correspondentes para cada tipo de tributo, conforme detalhado anteriormente.

## Implementação do Frontend

### Atualização da Página de Cenários

```javascript
/**
 * cenarios.js - Auditoria Fiscal
 * Funções para gerenciar as páginas de cenários
 */

document.addEventListener('DOMContentLoaded', function () {
  // Verificar se estamos em uma página de cenários
  const path = window.location.pathname;

  if (path.startsWith('/cenarios/')) {
    setupCenariosPage();
  }
});

/**
 * Configura a página de cenários
 */
function setupCenariosPage() {
  // Obter informações da URL
  const path = window.location.pathname;
  const parts = path.split('/');

  if (parts.length >= 3) {
    const direcao = parts[2]; // entrada ou saida

    // Configurar os cards de tributos
    setupTributoCards(direcao);
  }
}

/**
 * Configura os cards de tributos
 */
function setupTributoCards(direcao) {
  const cardLinks = document.querySelectorAll('.card-link-wrapper');

  cardLinks.forEach((link) => {
    const tipo = link.dataset.tipo;

    // Adicionar event listener para clicar no card
    link.addEventListener('click', function (event) {
      event.preventDefault();

      // Redirecionar para a página de detalhes do cenário
      window.location.href = `/cenarios/${direcao}/${tipo}`;
    });
  });
}
```

### Implementação da Página de Detalhes de Cenários

```javascript
/**
 * cenarios_detalhes.js - Auditoria Fiscal
 * Funções para gerenciar as páginas de detalhes de cenários
 */

// Variáveis globais
window.cenariosDetalhes = {
  cenariosTable: null,
  selectedCompany: null,
  selectedYear: null,
  selectedMonth: null,
  currentTipoTributo: null,
  currentDirecao: null,
  currentStatus: 'novo',
};

document.addEventListener('DOMContentLoaded', function () {
  // Verificar se estamos em uma página de detalhes de cenários
  const path = window.location.pathname;

  if (
    path.match(
      /^\/cenarios\/(entrada|saida)\/(icms|icms_st|ipi|pis|cofins|difal)$/,
    )
  ) {
    setupCenariosDetalhesPage();
  }
});

/**
 * Configura a página de detalhes de cenários
 */
function setupCenariosDetalhesPage() {
  // Obter informações da URL
  const path = window.location.pathname;
  const parts = path.split('/');

  window.cenariosDetalhes.currentDirecao = parts[2]; // entrada ou saida
  window.cenariosDetalhes.currentTipoTributo = parts[3]; // icms, icms_st, ipi, pis, cofins, difal

  // Carregar o conteúdo da página
  loadCenariosDetalhesContent();

  // Configurar filtros
  setupFilters();

  // Carregar dados iniciais (aba 'Novos')
  loadCenariosData('novo');

  // Configurar eventos para as abas
  setupTabEvents();
}

/**
 * Carrega o conteúdo da página de detalhes de cenários
 */
function loadCenariosDetalhesContent() {
  // Criar a estrutura HTML da página
  const pageContent = document.getElementById('page-content');

  // Título da página
  const titulo = getTituloTributo();

  // Conteúdo HTML
  pageContent.innerHTML = `
    <div id="page-cenarios-detalhes" class="page-section active">
      <div class="section-header">
        <h2><i class="fas fa-sitemap"></i> Cenários de ${
          window.cenariosDetalhes.currentDirecao === 'entrada'
            ? 'Entrada'
            : 'Saída'
        } - ${titulo}</h2>
      </div>

      <div class="content-area">
        <!-- Abas de status -->
        <ul class="nav nav-tabs" id="cenarios-tabs">
          <li class="nav-item">
            <a class="nav-link active" id="novos-tab" data-bs-toggle="tab" href="#novos">Novos</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" id="producao-tab" data-bs-toggle="tab" href="#producao">Produção</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" id="inconsistentes-tab" data-bs-toggle="tab" href="#inconsistentes">Inconsistentes</a>
          </li>
        </ul>

        <!-- Conteúdo das abas -->
        <div class="tab-content" id="cenarios-content">
          <div class="tab-pane fade show active" id="novos">
            <!-- Conteúdo da aba Novos -->
            <div class="filter-row">
              <input type="text" id="cenario-filter-novo" class="form-control" placeholder="Filtrar...">
            </div>
            <div id="cenarios-novo-container"></div>
          </div>

          <div class="tab-pane fade" id="producao">
            <!-- Conteúdo da aba Produção -->
            <div class="filter-row">
              <input type="text" id="cenario-filter-producao" class="form-control" placeholder="Filtrar...">
            </div>
            <div id="cenarios-producao-container"></div>
          </div>

          <div class="tab-pane fade" id="inconsistentes">
            <!-- Conteúdo da aba Inconsistentes -->
            <div class="filter-row">
              <input type="text" id="cenario-filter-inconsistente" class="form-control" placeholder="Filtrar...">
            </div>
            <div id="cenarios-inconsistente-container"></div>
          </div>
        </div>
      </div>
    </div>
  `;
}

/**
 * Carrega os dados dos cenários
 */
function loadCenariosData(status) {
  // Atualizar status atual
  window.cenariosDetalhes.currentStatus = status;

  // Obter parâmetros
  const empresa_id = window.cenariosDetalhes.selectedCompany;
  const tipo_tributo = window.cenariosDetalhes.currentTipoTributo;

  // Construir URL da API
  let apiUrl = `/api/cenarios/${tipo_tributo}?status=${status}`;

  if (empresa_id) {
    apiUrl += `&empresa_id=${empresa_id}`;
  }

  // Mostrar indicador de carregamento
  const container = document.getElementById(`cenarios-${status}-container`);
  container.innerHTML = '<div class="loading-indicator">Carregando...</div>';

  // Fazer requisição à API
  fetch(apiUrl, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      // Renderizar tabela de cenários
      renderCenariosTable(container, data.cenarios, status);
    })
    .catch((error) => {
      console.error('Erro ao carregar cenários:', error);
      container.innerHTML = `<div class="alert alert-danger">Erro ao carregar cenários: ${error.message}</div>`;
    });
}

/**
 * Renderiza a tabela de cenários
 */
function renderCenariosTable(container, cenarios, status) {
  // Criar tabela HTML
  let tableHtml = `
    <table id="cenario-${status}-table" class="table table-striped table-hover">
      <thead>
        <tr>
          <th>Cliente</th>
          <th>Produto</th>
          <th>Alíquota</th>
          <th>Data Início</th>
          <th>Ativo</th>
          <th>Ações</th>
        </tr>
      </thead>
      <tbody>
  `;

  // Adicionar linhas da tabela
  if (cenarios && cenarios.length > 0) {
    cenarios.forEach((cenario) => {
      tableHtml += `
        <tr>
          <td>${cenario.cliente ? cenario.cliente.razao_social : 'N/A'}</td>
          <td>${cenario.produto ? cenario.produto.descricao : 'N/A'}</td>
          <td>${
            cenario.aliquota ? cenario.aliquota.toFixed(2) + '%' : 'N/A'
          }</td>
          <td>${cenario.data_inicio_vigencia || 'N/A'}</td>
          <td>${
            cenario.ativo
              ? '<span class="badge bg-success">Sim</span>'
              : '<span class="badge bg-secondary">Não</span>'
          }</td>
          <td>
            ${getActionButtons(cenario, status)}
          </td>
        </tr>
      `;
    });
  } else {
    tableHtml += `
      <tr>
        <td colspan="6" class="text-center">Nenhum cenário encontrado</td>
      </tr>
    `;
  }

  tableHtml += `
      </tbody>
    </table>
  `;

  // Atualizar o container
  container.innerHTML = tableHtml;

  // Inicializar DataTable
  window.cenariosDetalhes.cenariosTable = $(
    `#cenario-${status}-table`,
  ).DataTable({
    language: {
      url: '/static/js/vendor/datatables/pt-BR.json',
    },
    responsive: true,
    order: [[0, 'asc']],
  });

  // Configurar filtro personalizado
  $(`#cenario-filter-${status}`).on('keyup', function () {
    window.cenariosDetalhes.cenariosTable.search(this.value).draw();
  });

  // Configurar eventos para os botões
  setupCenarioButtons(status);
}

/**
 * Retorna os botões de ação com base no status
 */
function getActionButtons(cenario, status) {
  let buttons = '';

  // Botão de edição (comum a todos os status)
  buttons += `<button class="btn btn-sm btn-primary edit-cenario-btn" data-cenario-id="${cenario.id}"><i class="fas fa-edit"></i></button> `;

  // Botões específicos por status
  if (status === 'novo') {
    // Botão para enviar para produção
    buttons += `<button class="btn btn-sm btn-success to-producao-btn" data-cenario-id="${cenario.id}"><i class="fas fa-check"></i></button>`;
  } else if (status === 'producao') {
    // Botão para ativar/desativar
    if (cenario.ativo) {
      buttons += `<button class="btn btn-sm btn-warning toggle-ativo-btn" data-cenario-id="${cenario.id}" data-ativo="true"><i class="fas fa-toggle-on"></i></button> `;
    } else {
      buttons += `<button class="btn btn-sm btn-secondary toggle-ativo-btn" data-cenario-id="${cenario.id}" data-ativo="false"><i class="fas fa-toggle-off"></i></button> `;
    }

    // Botão para atualizar vigência
    buttons += `<button class="btn btn-sm btn-info update-vigencia-btn" data-cenario-id="${cenario.id}"><i class="fas fa-calendar-alt"></i></button>`;
  } else if (status === 'inconsistente') {
    // Botão para excluir
    buttons += `<button class="btn btn-sm btn-danger delete-cenario-btn" data-cenario-id="${cenario.id}"><i class="fas fa-trash"></i></button> `;

    // Botão para enviar para produção
    buttons += `<button class="btn btn-sm btn-success to-producao-btn" data-cenario-id="${cenario.id}"><i class="fas fa-check"></i></button>`;
  }

  return buttons;
}
```

## Atualização do Processador XML

Para capturar o novo campo EXTIPI, precisamos atualizar o processador XML:

```python
class XMLProcessor:
    # ... código existente ...

    def get_produtos(self):
        """
        Extrai informações dos produtos da NFe

        Returns:
            list: Lista de dicionários com informações dos produtos
        """
        produtos = []

        # Buscar todos os itens
        itens = self.inf_nfe.findall('.//nfe:det', self.ns)

        for item in itens:
            # Extrair informações do produto
            prod = item.find('.//nfe:prod', self.ns)
            if prod is None:
                continue

            # Extrair código do produto
            codigo = self._get_text_from_element(prod, 'nfe:cProd')

            # Extrair EXTIPI (novo campo)
            extipi = self._get_text_from_element(prod, 'nfe:EXTIPI')

            # ... código existente para extrair outros campos ...

            # Extrair informações de impostos
            imposto = item.find('.//nfe:imposto', self.ns)
            impostos = self._get_impostos(imposto) if imposto is not None else {}

            # Adicionar EXTIPI aos impostos de IPI
            if 'ipi' in impostos:
                impostos['ipi']['ex'] = extipi

            # Criar dicionário do produto
            produto_dict = {
                'codigo': codigo,
                'descricao': descricao,
                # ... outros campos do produto ...
            }

            # Adicionar campos de impostos ao dicionário do produto
            produto_dict.update(impostos)

            produtos.append(produto_dict)

        return produtos
```

## Resumo da Reformulação

A reformulação do sistema de Auditoria Fiscal envolve uma mudança fundamental na forma como os cenários tributários são gerenciados. As principais mudanças são:

1. **Criação de tabelas específicas por tipo de tributo**: Em vez de usar uma única tabela de tributos com status para cada tipo, serão criadas tabelas separadas para cada tipo de tributo (ICMS, ICMS-ST, IPI, PIS, COFINS, DIFAL).

2. **Novos campos para tributos**:

   - Adição do campo `ipi_ex` para armazenar o valor da tag EXTIPI do XML
   - Adição dos campos `pis_p_red_bc` e `cofins_p_red_bc` para percentual de redução da base de cálculo
   - Campos calculados para cada tipo de tributo (alíquota e valor)

3. **Campos específicos por tipo de tributo**:

   - **ICMS**: origem, cst, mod_bc, p_red_bc, aliquota, p_dif
   - **ICMS-ST**: origem, cst, mod_bc, p_red_bc, aliquota, icms_st_mod_bc, icms_st_aliquota, icms_st_p_mva
   - **IPI**: cst, aliquota, ex
   - **PIS**: cst, aliquota, p_red_bc
   - **COFINS**: cst, aliquota, p_red_bc
   - **DIFAL**: origem, cst, mod_bc, p_red_bc, aliquota, p_fcp_uf_dest, p_icms_uf_dest, p_icms_inter, p_icms_inter_part

4. **Novo modelo de status**: Os cenários terão três status possíveis:

   - **Novo**: Cenários recém-criados que ainda não foram validados
   - **Produção**: Cenários validados e aprovados para uso
   - **Inconsistente**: Cenários que apresentam divergências em relação aos cenários em produção

5. **Vigência e ativação**: Os cenários em produção terão:

   - Data de início de vigência (obrigatória)
   - Data de fim de vigência (opcional)
   - Flag de ativo/inativo para controlar quais cenários são usados nos cálculos

6. **Cálculo de tributos**: Um novo serviço calculará os valores de tributos com base nos cenários ativos em produção, atualizando as colunas de valores calculados na tabela de tributos. Os cálculos incluem:

   - Aplicação de redução da base de cálculo quando disponível
   - Aplicação de MVA para ICMS-ST
   - Cálculo de DIFAL com base nas alíquotas de destino e interestadual
   - Cálculo de FCP quando aplicável

7. **Simplificação do status de produtos**: O status do produto será simplificado para 'conforme' ou 'nulo', indicando se todos os seus tributos foram calculados com base em cenários em produção e levando em consideração cenários em produção ativos e com tempo de vigência válido.

8. **Atualização do status de clientes**: O status do cliente refletirá o preenchimento dos campos de atividade e destinação, com valores possíveis: 'ok', 'sem_atividade', 'sem_destinacao', 'sem_ambos'.

9. **Processamento de XML aprimorado**: Captura da tag EXTIPI durante a importação de XML e associação ao tributo IPI.

10. **Interface de usuário aprimorada**: Novas páginas de detalhes de cenários com abas para os diferentes status e botões específicos para cada status.

Esta reformulação permitirá um controle mais granular dos cenários tributários, facilitando a auditoria fiscal e garantindo que os cálculos de tributos sejam realizados com base em cenários validados e ativos.

## Considerações Finais

A implementação desta reformulação deve ser realizada em fases, conforme detalhado no plano de implementação. É importante realizar testes extensivos após cada fase para garantir que as funcionalidades estejam operando corretamente.

Por fim, é essencial fornecer treinamento aos usuários sobre as mudanças no sistema, especialmente sobre o novo modelo de cenários e como gerenciar os diferentes status.
