"""
Rotas para monitoramento do sistema e controle de concorrência
"""
from flask import Blueprint, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, Usuario
from services.queue_manager import get_queue_manager
from services.transaction_manager import get_transaction_manager
import logging

logger = logging.getLogger(__name__)

system_bp = Blueprint('system_bp', __name__)

@system_bp.route('/api/system/status', methods=['GET'])
@jwt_required()
def get_system_status():
    """
    Obtém o status geral do sistema
    """
    try:
        # Verificar permissões (apenas admins)
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        if not usuario or not (usuario.is_admin or usuario.tipo_usuario == 'admin'):
            return jsonify({"message": "Acesso negado"}), 403
        
        # Obter gerenciadores
        queue_manager = get_queue_manager()
        transaction_manager = get_transaction_manager()
        
        # Coletar estatísticas
        queue_stats = queue_manager.get_queue_stats()
        transaction_stats = transaction_manager.get_stats()
        active_transactions = transaction_manager.get_active_transactions()
        long_running = transaction_manager.detect_long_running_transactions()
        
        return jsonify({
            "status": "ok",
            "queues": queue_stats,
            "transactions": {
                "stats": transaction_stats,
                "active_count": len(active_transactions),
                "long_running_count": len(long_running)
            },
            "database": {
                "pool_size": db.engine.pool.size(),
                "checked_in": db.engine.pool.checkedin(),
                "checked_out": db.engine.pool.checkedout(),
                "overflow": db.engine.pool.overflow(),
                "invalid": db.engine.pool.invalid()
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Erro ao obter status do sistema: {str(e)}")
        return jsonify({"message": "Erro interno do servidor"}), 500

@system_bp.route('/api/system/queues', methods=['GET'])
@jwt_required()
def get_queue_details():
    """
    Obtém detalhes das filas de processamento
    """
    try:
        # Verificar permissões (apenas admins)
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        if not usuario or not (usuario.is_admin or usuario.tipo_usuario == 'admin'):
            return jsonify({"message": "Acesso negado"}), 403
        
        queue_manager = get_queue_manager()
        stats = queue_manager.get_queue_stats()
        
        # Adicionar informações detalhadas sobre tarefas ativas
        active_tasks = []
        for task_id, task in queue_manager.active_tasks.items():
            active_tasks.append({
                "id": task.id,
                "type": task.type,
                "user_id": task.user_id,
                "empresa_id": task.empresa_id,
                "status": task.status.value,
                "priority": task.priority,
                "created_at": task.created_at,
                "started_at": task.started_at,
                "duration": (task.completed_at or task.started_at or 0) - (task.started_at or task.created_at or 0) if task.started_at else 0
            })
        
        return jsonify({
            "queue_stats": stats,
            "active_tasks": active_tasks
        }), 200
        
    except Exception as e:
        logger.error(f"Erro ao obter detalhes das filas: {str(e)}")
        return jsonify({"message": "Erro interno do servidor"}), 500

@system_bp.route('/api/system/transactions', methods=['GET'])
@jwt_required()
def get_transaction_details():
    """
    Obtém detalhes das transações ativas
    """
    try:
        # Verificar permissões (apenas admins)
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        if not usuario or not (usuario.is_admin or usuario.tipo_usuario == 'admin'):
            return jsonify({"message": "Acesso negado"}), 403
        
        transaction_manager = get_transaction_manager()
        
        active_transactions = transaction_manager.get_active_transactions()
        long_running = transaction_manager.detect_long_running_transactions()
        stats = transaction_manager.get_stats()
        
        return jsonify({
            "stats": stats,
            "active_transactions": active_transactions,
            "long_running_transactions": long_running
        }), 200
        
    except Exception as e:
        logger.error(f"Erro ao obter detalhes das transações: {str(e)}")
        return jsonify({"message": "Erro interno do servidor"}), 500

@system_bp.route('/api/system/tasks/<task_id>', methods=['GET'])
@jwt_required()
def get_task_status(task_id):
    """
    Obtém o status de uma tarefa específica
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404
        
        queue_manager = get_queue_manager()
        task = queue_manager.get_task_status(task_id)
        
        if not task:
            return jsonify({"message": "Tarefa não encontrada"}), 404
        
        # Verificar se o usuário pode ver esta tarefa
        if not (usuario.is_admin or usuario.tipo_usuario == 'admin' or task.user_id == usuario_id):
            return jsonify({"message": "Acesso negado"}), 403
        
        return jsonify({
            "id": task.id,
            "type": task.type,
            "status": task.status.value,
            "priority": task.priority,
            "created_at": task.created_at,
            "started_at": task.started_at,
            "completed_at": task.completed_at,
            "error": task.error,
            "duration": (task.completed_at or task.started_at or 0) - (task.started_at or task.created_at or 0) if task.started_at else 0
        }), 200
        
    except Exception as e:
        logger.error(f"Erro ao obter status da tarefa: {str(e)}")
        return jsonify({"message": "Erro interno do servidor"}), 500

@system_bp.route('/api/system/tasks/<task_id>/cancel', methods=['POST'])
@jwt_required()
def cancel_task(task_id):
    """
    Cancela uma tarefa (se ainda não iniciada)
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404
        
        queue_manager = get_queue_manager()
        task = queue_manager.get_task_status(task_id)
        
        if not task:
            return jsonify({"message": "Tarefa não encontrada"}), 404
        
        # Verificar se o usuário pode cancelar esta tarefa
        if not (usuario.is_admin or usuario.tipo_usuario == 'admin' or task.user_id == usuario_id):
            return jsonify({"message": "Acesso negado"}), 403
        
        success = queue_manager.cancel_task(task_id)
        
        if success:
            return jsonify({"message": "Tarefa cancelada com sucesso"}), 200
        else:
            return jsonify({"message": "Não foi possível cancelar a tarefa (já iniciada ou concluída)"}), 400
        
    except Exception as e:
        logger.error(f"Erro ao cancelar tarefa: {str(e)}")
        return jsonify({"message": "Erro interno do servidor"}), 500

@system_bp.route('/api/system/cleanup', methods=['POST'])
@jwt_required()
def cleanup_system():
    """
    Força limpeza de recursos antigos (apenas para admins)
    """
    try:
        # Verificar permissões (apenas admins)
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)
        
        if not usuario or not (usuario.is_admin or usuario.tipo_usuario == 'admin'):
            return jsonify({"message": "Acesso negado"}), 403
        
        queue_manager = get_queue_manager()
        transaction_manager = get_transaction_manager()
        
        # Limpeza de tarefas antigas
        queue_manager.cleanup_completed_tasks(max_age_hours=24)
        
        # Limpeza de transações antigas (apenas em emergência)
        cleaned_transactions = transaction_manager.force_cleanup_old_locks(max_age=3600)
        
        return jsonify({
            "message": "Limpeza realizada com sucesso",
            "cleaned_transactions": cleaned_transactions
        }), 200
        
    except Exception as e:
        logger.error(f"Erro na limpeza do sistema: {str(e)}")
        return jsonify({"message": "Erro interno do servidor"}), 500

@system_bp.route('/api/system/health', methods=['GET'])
def health_check():
    """
    Endpoint de health check (sem autenticação)
    """
    try:
        # Teste básico de conectividade com o banco
        db.session.execute(db.text('SELECT 1'))
        
        return jsonify({
            "status": "healthy",
            "database": "connected"
        }), 200
        
    except Exception as e:
        logger.error(f"Health check falhou: {str(e)}")
        return jsonify({
            "status": "unhealthy",
            "database": "disconnected",
            "error": str(e)
        }), 503
