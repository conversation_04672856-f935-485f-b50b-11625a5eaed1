/**
 * Cenarios.js - Auditoria Fiscal
 * Funções para gerenciar as páginas de cenários
 */

// Variáveis globais - usando namespace para evitar conflitos
window.cenarios = window.cenarios || {
  selectedCompany: null,
  selectedYear: null,
  selectedMonth: null,
};

document.addEventListener('DOMContentLoaded', function () {

  // Configurar os links dos cards
  setupCardLinks();

  // Configurar filtros
  setupFilters();

  // Carregar contadores para os cards
  loadCardCounters();

  // Escutar mudanças de empresa do dashboard
  window.addEventListener('company-changed', function (event) {
    window.cenarios.selectedCompany = event.detail.companyId;
    loadCardCounters();
  });
});

/**
 * Configura os links dos cards
 */
function setupCardLinks() {
  const cardLinks = document.querySelectorAll('.card-link-wrapper');

  cardLinks.forEach((link) => {
    // Não precisamos adicionar event listeners, pois os links já funcionam nativamente
    // Apenas para logging
    const tipo = link.dataset.tipo;
    const isEntrada = link.closest('#page-cenarios-entrada') !== null;
  });
}

/**
 * Configura os filtros da página
 */
function setupFilters() {
  // Obter os filtros do header e da variável global
  // Usar a variável global selectedCompany definida em common.js ou dashboard.js
  window.cenarios.selectedCompany =
    window.selectedCompany || localStorage.getItem('selectedCompany');

  // Obter os valores dos seletores de ano e mês
  window.cenarios.selectedYear = document.getElementById('year-select')?.value;
  window.cenarios.selectedMonth =
    document.getElementById('month-select')?.value;


  // Adicionar event listeners para os filtros
  const companySelect = document.getElementById('company-select');
  if (companySelect) {
    companySelect.addEventListener('change', function () {
      window.cenarios.selectedCompany = this.value;
      loadCardCounters();
    });
  }

  const yearSelect = document.getElementById('year-select');
  if (yearSelect) {
    yearSelect.addEventListener('change', function () {
      window.cenarios.selectedYear = this.value;
      loadCardCounters();
    });
  }

  const monthSelect = document.getElementById('month-select');
  if (monthSelect) {
    monthSelect.addEventListener('change', function () {
      window.cenarios.selectedMonth = this.value;
      loadCardCounters();
    });
  }
}

/**
 * Carrega os contadores para os cards
 */
function loadCardCounters() {
  // Verificar se há uma empresa selecionada
  if (!window.cenarios.selectedCompany) {
    return;
  }

  // Obter todos os cards
  const cards = document.querySelectorAll('.card-link-wrapper');

  // Para cada card, carregar o contador
  cards.forEach((card) => {
    const tipo = card.dataset.tipo;
    const direcao =
      card.closest('#page-cenarios-entrada') !== null ? 'entrada' : 'saida';

    // Obter os elementos dos contadores
    const novoCounterElement = card.querySelector(
      '.counter-item.novo .counter-value',
    );
    const producaoCounterElement = card.querySelector(
      '.counter-item.producao .counter-value',
    );

    if (!novoCounterElement || !producaoCounterElement) return;

    // Mostrar indicador de carregamento
    novoCounterElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    producaoCounterElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // Parâmetros para a requisição
    const params = new URLSearchParams();
    params.append('empresa_id', window.cenarios.selectedCompany);
    params.append('direcao', direcao);

    // Adicionar filtros de ano e mês se estiverem definidos
    if (window.cenarios.selectedYear) {
      params.append('year', window.cenarios.selectedYear);
    }

    if (window.cenarios.selectedMonth) {
      params.append('month', window.cenarios.selectedMonth);
    }

    // Fazer requisição para a API
    fetch(`/api/cenarios/${tipo}/count?${params.toString()}`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          // Atualizar os contadores
          novoCounterElement.textContent = data.counts.novo || 0;
          producaoCounterElement.textContent = data.counts.producao || 0;
        } else {
          // Mostrar erro
          novoCounterElement.textContent = '-';
          producaoCounterElement.textContent = '-';
        }
      })
      .catch((error) => {
        console.error(
          `Erro ao carregar contadores para ${tipo} (${direcao}):`,
          error,
        );
        novoCounterElement.textContent = '-';
        producaoCounterElement.textContent = '-';
      });
  });
}
