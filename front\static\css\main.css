/*
 * Main CSS - Auditoria Fiscal
 * Estilos globais para todo o sistema
 */

:root {
    /* === SISTEMA DE CORES PRINCIPAL === */
    /* Azul - Cor primária do sistema */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    --primary-950: #172554;

    /* Aliases para compatibilidade */
    --primary-color: var(--primary-500);
    --primary-dark: var(--primary-600);
    --primary-light: var(--primary-400);

    /* Gradientes primários */
    --primary-gradient: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-500) 50%, var(--primary-400) 100%);
    --primary-gradient-subtle: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
    --primary-gradient-dark: linear-gradient(135deg, var(--primary-800) 0%, var(--primary-700) 50%, var(--primary-600) 100%);

    /* === CORES SEMÂNTICAS === */
    --success-50: #ecfdf5;
    --success-100: #d1fae5;
    --success-200: #a7f3d0;
    --success-500: #10b981;
    --success-600: #059669;
    --success-800: #065f46;
    --success-color: var(--success-500);

    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-200: #fde68a;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-800: #92400e;
    --warning-color: var(--warning-500);

    --danger-50: #fef2f2;
    --danger-100: #fee2e2;
    --danger-200: #fecaca;
    --danger-500: #ef4444;
    --danger-600: #dc2626;
    --danger-800: #991b1b;
    --danger-color: var(--danger-500);

    --info-50: #f0f9ff;
    --info-100: #e0f2fe;
    --info-200: #bae6fd;
    --info-500: #06b6d4;
    --info-600: #0891b2;
    --info-800: #155e75;
    --info-color: var(--info-500);

    /* === ESCALA DE CINZAS === */
    --gray-25: #fafbfc;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-750: #2a3441;
    --gray-800: #1e293b;
    --gray-850: #172033;
    --gray-900: #0f172a;
    --gray-950: #020617;

    /* Aliases para compatibilidade */
    --light-color: var(--gray-50);
    --dark-color: var(--gray-800);
    --secondary-color: var(--gray-500);

    /* === TOKENS DE DESIGN === */
    /* Layout */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --header-height: 72px;

    /* Espaçamentos */
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 1rem;       /* 16px */
    --space-lg: 1.5rem;     /* 24px */
    --space-xl: 2rem;       /* 32px */
    --space-2xl: 3rem;      /* 48px */

    /* Border radius */
    --radius-sm: 0.25rem;   /* 4px */
    --radius-md: 0.375rem;  /* 6px */
    --radius-lg: 0.5rem;    /* 8px */
    --radius-xl: 0.75rem;   /* 12px */
    --radius-2xl: 1rem;     /* 16px */
    --radius-3xl: 1.5rem;   /* 24px */

    /* Compatibilidade */
    --border-radius: var(--radius-md);

    /* Sombras */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Sombras coloridas */
    --shadow-primary: 0 10px 15px -3px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
    --shadow-success: 0 10px 15px -3px rgba(16, 185, 129, 0.1), 0 4px 6px -2px rgba(16, 185, 129, 0.05);
    --shadow-warning: 0 10px 15px -3px rgba(245, 158, 11, 0.1), 0 4px 6px -2px rgba(245, 158, 11, 0.05);
    --shadow-danger: 0 10px 15px -3px rgba(239, 68, 68, 0.1), 0 4px 6px -2px rgba(239, 68, 68, 0.05);

    /* Compatibilidade */
    --box-shadow: var(--shadow-md);

    /* Transições */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;

    /* Compatibilidade */
    --transition-speed: var(--transition-normal);

    /* Tipografia */
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
    --font-size-4xl: 2.25rem;   /* 36px */

    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
}

/* Reset e estilos base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--gray-700);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-speed);
}

a:hover {
    color: var(--primary-dark);
}

/* === BOTÕES MODERNOS === */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-tight);
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    text-decoration: none;
}

.btn:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* Botão primário com gradiente */
.btn-primary {
    color: white;
    background: var(--primary-gradient);
    border-color: var(--primary-600);
    box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
    background: var(--primary-gradient-dark);
    border-color: var(--primary-700);
    box-shadow: var(--shadow-lg);
    transform: translateY(-1px);
}

.btn-primary:focus {
    box-shadow: var(--shadow-primary), 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

/* Botão de sucesso */
.btn-success {
    color: white;
    background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);
    border-color: var(--success-600);
    box-shadow: var(--shadow-success);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 50%, #0ca678 100%);
    border-color: #0ca678;
    box-shadow: var(--shadow-lg);
    transform: translateY(-1px);
}

/* Botão de perigo */
.btn-danger {
    color: white;
    background: linear-gradient(135deg, var(--danger-600) 0%, var(--danger-500) 100%);
    border-color: var(--danger-600);
    box-shadow: var(--shadow-danger);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, var(--danger-600) 50%, var(--danger-500) 100%);
    border-color: #dc2626;
    box-shadow: var(--shadow-lg);
    transform: translateY(-1px);
}

/* Botão de aviso */
.btn-warning {
    color: white;
    background: linear-gradient(135deg, var(--warning-600) 0%, var(--warning-500) 100%);
    border-color: var(--warning-600);
    box-shadow: var(--shadow-warning);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #d97706 0%, var(--warning-600) 50%, var(--warning-500) 100%);
    border-color: #d97706;
    box-shadow: var(--shadow-lg);
    transform: translateY(-1px);
}

/* Botão secundário */
.btn-secondary {
    color: var(--gray-700);
    background-color: white;
    border-color: var(--gray-300);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background-color: var(--gray-50);
    border-color: var(--gray-400);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

/* Botão outline */
.btn-outline-primary {
    color: var(--primary-600);
    background-color: transparent;
    border-color: var(--primary-300);
}

.btn-outline-primary:hover {
    color: white;
    background: var(--primary-gradient);
    border-color: var(--primary-600);
    box-shadow: var(--shadow-primary);
}

/* Tamanhos de botão */
.btn-sm {
    padding: var(--space-xs) var(--space-md);
    font-size: var(--font-size-xs);
    border-radius: var(--radius-md);
}

.btn-lg {
    padding: var(--space-md) var(--space-xl);
    font-size: var(--font-size-base);
    border-radius: var(--radius-xl);
}

/* Botão com ícone */
.btn i {
    font-size: 0.875em;
}

.btn .btn-icon {
    width: 1em;
    height: 1em;
}

/* Grupo de botões */
.btn-group {
    display: inline-flex;
    gap: var(--space-xs);
    flex-wrap: nowrap;
}

.btn-group .btn {
    margin: 0;
}

/* === FORMULÁRIOS MODERNOS === */
.form-control {
    display: block;
    width: 100%;
    /* padding: var(--space-md) var(--space-lg); */
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--gray-700);
    background-color: white;
    background-clip: padding-box;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.form-control:focus {
    color: var(--gray-800);
    background-color: white;
    border-color: var(--primary-400);
    outline: 0;
    box-shadow: var(--shadow-md), 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.form-control::placeholder {
    color: var(--gray-400);
    opacity: 1;
}s

.form-select {
    display: block;
    width: 100%;
    /* padding: var(--space-md) var(--space-xl) var(--space-md) var(--space-lg); */
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--gray-700);
    background-color: white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right var(--space-md) center;
    background-size: 16px 12px;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
    appearance: none;
}

.form-select:focus {
    border-color: var(--primary-400);
    outline: 0;
    box-shadow: var(--shadow-md), 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-label {
    display: block;
    margin-bottom: var(--space-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--gray-700);
    line-height: var(--line-height-tight);
}

/* Grupos de input */
.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: var(--space-md) var(--space-lg);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--gray-600);
    text-align: center;
    white-space: nowrap;
    background-color: var(--gray-100);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
}

/* Utilitários */
.text-primary { color: var(--primary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-info { background-color: var(--info-color) !important; }

.d-none { display: none !important; }
.d-flex { display: flex !important; }
.flex-column { flex-direction: column !important; }
.justify-content-between { justify-content: space-between !important; }
.align-items-center { align-items: center !important; }

.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.ms-1 { margin-left: 0.25rem !important; }
.ms-2 { margin-left: 0.5rem !important; }
.ms-3 { margin-left: 1rem !important; }

.me-1 { margin-right: 0.25rem !important; }
.me-2 { margin-right: 0.5rem !important; }
.me-3 { margin-right: 1rem !important; }

.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

/* Alertas */
.alert {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
}

.alert-success {
    color: #0f5132;
    background-color: #d1e7dd;
    border-color: #badbcc;
}

.alert-danger {
    color: #842029;
    background-color: #f8d7da;
    border-color: #f5c2c7;
}

.alert-warning {
    color: #664d03;
    background-color: #fff3cd;
    border-color: #ffecb5;
}

.alert-info {
    color: #055160;
    background-color: #cff4fc;
    border-color: #b6effb;
}

/* === CARDS MODERNOS === */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background: linear-gradient(145deg, white 0%, var(--gray-50) 100%);
    background-clip: border-box;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-200);
}

.card-body {
    flex: 1 1 auto;
    padding: var(--space-xl);
    position: relative;
}

.card-title {
    margin-bottom: var(--space-md);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    line-height: var(--line-height-tight);
}

.card-subtitle {
    margin-bottom: var(--space-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-normal);
    color: var(--gray-600);
    line-height: var(--line-height-normal);
}

.card-text {
    margin-bottom: var(--space-lg);
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    line-height: var(--line-height-relaxed);
}

.card-header {
    padding: var(--space-lg) var(--space-xl);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-bottom: 1px solid var(--gray-200);
    border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.card-footer {
    padding: var(--space-lg) var(--space-xl);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-top: 1px solid var(--gray-200);
    border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

/* Card com destaque */
.card-primary {
    border-color: var(--primary-200);
    background: linear-gradient(145deg, var(--primary-50) 0%, white 100%);
}

.card-primary .card-header {
    background: var(--primary-gradient-subtle);
    border-bottom-color: var(--primary-200);
}

.card-primary .card-footer {
    background: var(--primary-gradient-subtle);
    border-top-color: var(--primary-200);
}

/* Card compacto */
.card-compact .card-body {
    padding: var(--space-lg);
}

.card-compact .card-header,
.card-compact .card-footer {
    padding: var(--space-md) var(--space-lg);
}

/* === TABELAS MODERNAS === */
.table {
    width: 100%;
    margin-bottom: var(--space-lg);
    color: var(--gray-700);
    vertical-align: top;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    background-color: white;
}

.table th,
.table td {
    padding: var(--space-lg) var(--space-md);
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-100);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
}

.table thead th {
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-50) 100%);
    border-bottom: 2px solid var(--gray-200);
    text-transform: uppercase;
    letter-spacing: 0.025em;
    font-size: var(--font-size-xs);
    position: sticky;
    top: 0;
    z-index: 10;
}

.table thead th:first-child {
    border-top-left-radius: var(--radius-xl);
}

.table thead th:last-child {
    border-top-right-radius: var(--radius-xl);
}

.table tbody tr {
    transition: all var(--transition-fast);
}

.table tbody tr:hover {
    background-color: var(--primary-50);
    /* transform: scale(1.01); */
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.1);
}

.table tbody tr:last-child td:first-child {
    border-bottom-left-radius: var(--radius-xl);
}

.table tbody tr:last-child td:last-child {
    border-bottom-right-radius: var(--radius-xl);
}

/* Tabela responsiva */
.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
}

/* Tabela striped moderna */
.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--gray-25);
}

.table-striped tbody tr:nth-of-type(even) {
    background-color: white;
}

/* Tabela bordered moderna */
.table-bordered {
    border: 1px solid var(--gray-200);
}

.table-bordered th,
.table-bordered td {
    border: 1px solid var(--gray-200);
}

/* Estados da tabela */
.table .table-active {
    background-color: var(--primary-100);
    color: var(--primary-800);
}

.table .table-success {
    background-color: var(--success-100);
    color: var(--success-800);
}

.table .table-warning {
    background-color: var(--warning-100);
    color: var(--warning-800);
}

.table .table-danger {
    background-color: var(--danger-100);
    color: var(--danger-800);
}

/* === TEMA ESCURO MODERNO === */
body.dark-theme {
    color: #e2e8f0;
    background: #000000;
}

/* Links no tema escuro */
body.dark-theme a {
    color: var(--primary-400);
}

body.dark-theme a:hover {
    color: var(--primary-300);
}

/* Botões no tema escuro */
body.dark-theme .btn-primary {
    background: var(--primary-gradient);
    border-color: var(--primary-600);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

body.dark-theme .btn-primary:hover {
    background: var(--primary-gradient-dark);
    border-color: var(--primary-700);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

body.dark-theme .btn-secondary {
    color: #e2e8f0;
    background-color: #1e293b;
    border-color: #475569;
}

body.dark-theme .btn-secondary:hover {
    background-color: #334155;
    border-color: #64748b;
}

/* Formulários no tema escuro */
body.dark-theme .form-control {
    background-color: #1e293b;
    border-color: #475569;
    color: #e2e8f0;
}

body.dark-theme .form-control:focus {
    background-color: #1e293b;
    border-color: var(--primary-500);
    color: #f1f5f9;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 0 0 3px rgba(59, 130, 246, 0.2);
}

body.dark-theme .form-control::placeholder {
    color: #64748b;
}

body.dark-theme .form-select {
    background-color: #1e293b;
    border-color: #475569;
    color: #e2e8f0;
}

body.dark-theme .form-select:focus {
    border-color: var(--primary-500);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 0 0 3px rgba(59, 130, 246, 0.2);
}

body.dark-theme .form-label {
    color: #cbd5e1;
}

body.dark-theme .input-group-text {
    background-color: #334155;
    border-color: #475569;
    color: #94a3b8;
}

/* Cards no tema escuro */
body.dark-theme .card {
    background: #1e293b;
    border-color: #475569;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

body.dark-theme .card:hover {
    border-color: var(--primary-600);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.4);
}

body.dark-theme .card-header {
    background: #334155;
    border-bottom-color: #475569;
}

body.dark-theme .card-footer {
    background: #334155;
    border-top-color: #475569;
}

body.dark-theme .card-title {
    color: #e2e8f0;
}

body.dark-theme .card-subtitle {
    color: #94a3b8;
}

body.dark-theme .card-text {
    color: #cbd5e1;
}

/* Tabelas no tema escuro */
body.dark-theme .table {
    color: #cbd5e1;
    background-color: #1e293b;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

body.dark-theme .table th,
body.dark-theme .table td {
    border-color: #475569;
}

body.dark-theme .table thead th {
    color: #e2e8f0;
    background: #334155;
    border-bottom-color: #475569;
}

body.dark-theme .table tbody tr:hover {
    background-color: #334155;
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
}

body.dark-theme .table-striped tbody tr:nth-of-type(odd) {
    background-color: #172033;
}

body.dark-theme .table-striped tbody tr:nth-of-type(even) {
    background-color: #1e293b;
}

/* Dropdowns no tema escuro */
body.dark-theme .dropdown-menu {
    background-color: #1e293b;
    border-color: #475569;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

body.dark-theme .dropdown-item {
    color: #cbd5e1;
}

body.dark-theme .dropdown-item:hover {
    background-color: #334155;
    color: #e2e8f0;
}

body.dark-theme .dropdown-divider {
    border-color: #475569;
}

/* Alertas no tema escuro */
body.dark-theme .alert-success {
    color: var(--success-200);
    background-color: rgba(16, 185, 129, 0.1);
    border-color: var(--success-600);
}

body.dark-theme .alert-danger {
    color: var(--danger-200);
    background-color: rgba(239, 68, 68, 0.1);
    border-color: var(--danger-600);
}

body.dark-theme .alert-warning {
    color: var(--warning-200);
    background-color: rgba(245, 158, 11, 0.1);
    border-color: var(--warning-600);
}

body.dark-theme .alert-info {
    color: var(--info-200);
    background-color: rgba(6, 182, 212, 0.1);
    border-color: var(--info-600);
}
