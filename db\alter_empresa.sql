-- Alteração da tabela empresa para adicionar novos campos
ALTER TABLE empresa
DROP COLUMN razao_social VARCHAR(255),
ADD COLUMN nome_fantasia VARCHAR(255),
ADD COLUMN cep VARCHAR(10),
ADD COLUMN logradouro VARCHAR(255),
ADD COLUMN numero VARCHAR(20),
ADD COLUMN complemento VARCHAR(255),
ADD COLUMN bairro VARCHAR(100),
ADD COLUMN cidade VARCHAR(100),
ADD COLUMN estado VARCHAR(2),
ADD COLUMN cnae VARCHAR(20),
ADD COLUMN tributacao VARCHAR(50),
ADD COLUMN atividade VARCHAR(50);

-- Atualizar registros existentes para preencher razao_social com o valor de nome
UPDATE empresa SET razao_social = nome WHERE razao_social IS NULL;

-- Comentários nas colunas
COMMENT ON COLUMN empresa.razao_social IS 'Razão Social da empresa';
COMMENT ON COLUMN empresa.nome_fantasia IS 'Nome Fantasia da empresa';
COMMENT ON COLUMN empresa.cep IS 'CEP do endereço';
COMMENT ON COLUMN empresa.logradouro IS 'Logradouro do endereço';
COMMENT ON COLUMN empresa.numero IS 'Número do endereço';
COMMENT ON COLUMN empresa.complemento IS 'Complemento do endereço';
COMMENT ON COLUMN empresa.bairro IS 'Bairro do endereço';
COMMENT ON COLUMN empresa.cidade IS 'Cidade do endereço';
COMMENT ON COLUMN empresa.estado IS 'Estado (UF) do endereço';
COMMENT ON COLUMN empresa.cnae IS 'Código CNAE da empresa';
COMMENT ON COLUMN empresa.tributacao IS 'Regime de tributação (Lucro Presumido, Lucro Real, Simples, Simples Nacional, Sub-Limite)';
COMMENT ON COLUMN empresa.atividade IS 'Tipo de atividade (Não Aplicado, Indústria, Comércio Varejista, Comércio Atacadista, Distribuidor, Produtor Rural, Não Contribuinte)';
