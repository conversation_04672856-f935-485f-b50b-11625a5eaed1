"""
Modelo para controle manual de status de auditoria
"""

from .escritorio import db
from sqlalchemy.sql import func
from datetime import datetime

class AuditoriaStatusManual(db.Model):
    __tablename__ = 'auditoria_status_manual'
    
    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.<PERSON>umn(db.<PERSON><PERSON>, db.<PERSON><PERSON>('empresa.id'), nullable=False)
    tipo_tributo = db.<PERSON>umn(db.Enum('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal'), nullable=False)
    status = db.Column(db.Enum('nao_aplicavel', 'aplicavel'), nullable=False, default='aplicavel')
    motivo = db.Column(db.Text)
    data_marcacao = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    usuario_id = db.<PERSON>umn(db.<PERSON>, db.<PERSON><PERSON><PERSON>('usuario.id'), nullable=False)
    
    # Relacionamentos
    empresa = db.relationship('Empresa', backref='status_manuais')
    usuario = db.relationship('Usuario', backref='marcacoes_status')
    
    # Constraint única
    __table_args__ = (
        db.UniqueConstraint('empresa_id', 'tipo_tributo', name='unique_empresa_tributo'),
    )
    
    def to_dict(self):
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'tipo_tributo': self.tipo_tributo,
            'status': self.status,
            'motivo': self.motivo,
            'data_marcacao': self.data_marcacao.isoformat() if self.data_marcacao else None,
            'usuario_id': self.usuario_id,
            'usuario_nome': self.usuario.nome if self.usuario else None
        }
    
    @staticmethod
    def obter_status_empresa(empresa_id):
        """
        Obtém todos os status manuais de uma empresa
        """
        return AuditoriaStatusManual.query.filter_by(empresa_id=empresa_id).all()
    
    @staticmethod
    def obter_status_tributo(empresa_id, tipo_tributo):
        """
        Obtém o status manual de um tributo específico
        """
        return AuditoriaStatusManual.query.filter_by(
            empresa_id=empresa_id,
            tipo_tributo=tipo_tributo
        ).first()
    
    @staticmethod
    def marcar_status(empresa_id, tipo_tributo, status, motivo, usuario_id):
        """
        Marca ou atualiza o status manual de um tributo
        """
        # Verificar se já existe
        status_existente = AuditoriaStatusManual.obter_status_tributo(empresa_id, tipo_tributo)
        
        if status_existente:
            # Atualizar existente
            status_existente.status = status
            status_existente.motivo = motivo
            status_existente.data_marcacao = datetime.utcnow()
            status_existente.usuario_id = usuario_id
        else:
            # Criar novo
            status_existente = AuditoriaStatusManual(
                empresa_id=empresa_id,
                tipo_tributo=tipo_tributo,
                status=status,
                motivo=motivo,
                usuario_id=usuario_id
            )
            db.session.add(status_existente)
        
        db.session.commit()
        return status_existente
    
    @staticmethod
    def remover_status(empresa_id, tipo_tributo):
        """
        Remove o status manual (volta ao padrão)
        """
        status_existente = AuditoriaStatusManual.obter_status_tributo(empresa_id, tipo_tributo)
        if status_existente:
            db.session.delete(status_existente)
            db.session.commit()
            return True
        return False
