from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, Usuario, Tributo, Cliente, Produto, TributoHistorico, Empresa, AuditoriaResultado, AuditoriaSumario, NotaFiscalItem
from models import CenarioICMS, CenarioICMSST, CenarioIPI, CenarioPIS, CenarioCOFINS, CenarioDIFAL
from services.auditoria_service import AuditoriaService
from datetime import datetime
import logging
import threading
import uuid
from sqlalchemy import and_, or_

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Criar blueprint
auditoria_bp = Blueprint('auditoria', __name__)

@auditoria_bp.route('/api/auditoria/executar', methods=['POST'])
@jwt_required()
def executar_auditoria():
    """
    Executa a auditoria fiscal para os tributos especificados de forma assíncrona
    """
    try:
        data = request.get_json()

        # Obter parâmetros
        empresa_id = data.get('empresa_id')
        tributo_ids = data.get('tributo_ids')
        produto_ids = data.get('produto_ids')
        cliente_ids = data.get('cliente_ids')
        forcar_recalculo = data.get('forcar_recalculo', False)
        tipo_tributo = data.get('tipo_tributo')
        tipo_operacao = data.get('tipo_operacao')  # 0 = entrada, 1 = saída
        year = data.get('year')
        month = data.get('month')

        # Validar parâmetros
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 404

        # Gerar ID único para esta auditoria
        audit_id = str(uuid.uuid4())

        # Obter serviço WebSocket
        from services.websocket_service import get_websocket_service
        websocket_service = get_websocket_service()

        # Criar callback de progresso
        progress_callback = None
        if websocket_service:
            progress_callback = websocket_service.create_audit_progress_callback(audit_id)

        # Se tivermos tipo_tributo, tipo_operacao, year e month, buscar os tributos correspondentes
        if tipo_tributo and tipo_operacao is not None and year and month:
            logger.info(f"Executando auditoria para empresa_id={empresa_id}, tipo_tributo={tipo_tributo}, tipo_operacao={tipo_operacao}, year={year}, month={month}")

            # Construir query para buscar tributos do tipo especificado
            query = Tributo.query.filter_by(empresa_id=empresa_id)

            # Filtrar por tipo de operação (entrada/saída)
            query = query.filter_by(tipo_operacao=str(tipo_operacao))

            # Filtrar por data
            query = query.filter(
                db.extract('year', Tributo.data_emissao) == year,
                db.extract('month', Tributo.data_emissao) == month
            )

            # Buscar tributos
            tributos = query.all()

            if not tributos:
                return jsonify({
                    'success': False,
                    'message': f'Nenhum tributo encontrado para os filtros especificados'
                }), 404

            # Obter IDs dos tributos
            tributo_ids = [tributo.id for tributo in tributos]

            logger.info(f"Encontrados {len(tributo_ids)} tributos para auditoria")

        # Retornar imediatamente o audit_id para o frontend configurar WebSocket
        # e processar auditoria em background

        # Capturar o contexto da aplicação antes de criar a thread
        app = current_app._get_current_object()

        def process_audit_in_background():
            """Processa auditoria em background"""
            try:
                # Executar dentro do contexto da aplicação
                with app.app_context():
                    logger.info(f"[AUDIT] Processando auditoria em background para {len(tributo_ids) if tributo_ids else 'todos'} tributos...")

                    # Inicializar serviço de auditoria com callback
                    auditoria_service = AuditoriaService(empresa_id, usuario_id, progress_callback)

                    # Executar auditoria
                    resultado = auditoria_service.executar_auditoria(
                        tributo_ids=tributo_ids,
                        produto_ids=produto_ids,
                        cliente_ids=cliente_ids,
                        forcar_recalculo=forcar_recalculo,
                        tipo_tributo=tipo_tributo
                    )

                    # Enviar notificação de conclusão via WebSocket
                    if websocket_service:
                        websocket_service.send_audit_complete(audit_id, {
                            "message": "Auditoria concluída com sucesso",
                            "results": resultado
                        })

            except Exception as e:
                logger.error(f"[AUDIT] Erro no processamento em background: {str(e)}")
                # Enviar notificação de erro via WebSocket
                if websocket_service:
                    websocket_service.send_audit_error(audit_id, {
                        "message": f"Erro na auditoria: {str(e)}"
                    })

        # Iniciar processamento em background
        thread = threading.Thread(target=process_audit_in_background)
        thread.daemon = True
        thread.start()

        # Retornar resposta imediata
        return jsonify({
            'success': True,
            'audit_id': audit_id,
            'message': 'Auditoria iniciada',
            'status': 'processing'
        }), 202

    except Exception as e:
        logger.error(f"Erro ao iniciar auditoria: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao iniciar auditoria: {str(e)}'
        }), 500

@auditoria_bp.route('/api/auditoria/tributos', methods=['GET', 'POST'])
@jwt_required()
def listar_tributos_auditados():
    """
    GET: Lista os tributos auditados
    POST: Executa a auditoria fiscal para os tributos de um tipo específico
    """
    # Se for uma requisição POST, executar a auditoria
    if request.method == 'POST':
        try:
            data = request.get_json()

            # Obter parâmetros
            empresa_id = data.get('empresa_id')
            tipo_tributo = data.get('tipo_tributo')
            direcao = data.get('direcao')
            forcar_recalculo = data.get('forcar_recalculo', False)

            # Validar parâmetros
            if not empresa_id:
                return jsonify({
                    'success': False,
                    'message': 'ID da empresa não informado'
                }), 400

            if not tipo_tributo:
                return jsonify({
                    'success': False,
                    'message': 'Tipo de tributo não informado'
                }), 400

            if not direcao:
                return jsonify({
                    'success': False,
                    'message': 'Direção não informada'
                }), 400

            # Verificar permissões
            usuario_id = get_jwt_identity()
            usuario = db.session.get(Usuario, usuario_id)

            if not usuario:
                return jsonify({
                    'success': False,
                    'message': 'Usuário não encontrado'
                }), 404

            # Gerar ID único para esta auditoria
            audit_id = str(uuid.uuid4())

            # Obter serviço WebSocket
            from services.websocket_service import get_websocket_service
            websocket_service = get_websocket_service()

            # Criar callback de progresso
            progress_callback = None
            if websocket_service:
                progress_callback = websocket_service.create_audit_progress_callback(audit_id)

            # Construir query para buscar tributos do tipo especificado
            query = Tributo.query.filter_by(empresa_id=empresa_id)

            logger.info(f"Buscando tributos para empresa_id={empresa_id}, tipo_tributo={tipo_tributo}, direcao={direcao}")

            # Filtrar por direção (entrada/saída)
            if direcao == 'entrada':
                query = query.filter_by(tipo_operacao='0')
            elif direcao == 'saida':
                query = query.filter_by(tipo_operacao='1')

            # Verificar quantos tributos existem antes de aplicar o filtro de tipo
            count_before_type_filter = query.count()
            logger.info(f"Encontrados {count_before_type_filter} tributos para empresa_id={empresa_id} e direcao={direcao}")

            # Filtrar por tipo de tributo
            if tipo_tributo == 'icms':
                # Para ICMS, não precisamos necessariamente de um cenário já associado
                # Vamos buscar todos os tributos que podem ter ICMS
                pass
            elif tipo_tributo == 'icms_st':
                # Para ICMS-ST, não precisamos necessariamente de um cenário já associado
                # Vamos buscar todos os tributos que podem ter ICMS-ST
                pass
            elif tipo_tributo == 'ipi':
                # Para IPI, não precisamos necessariamente de um cenário já associado
                # Vamos buscar todos os tributos que podem ter IPI
                pass
            elif tipo_tributo == 'pis':
                # Para PIS, não precisamos necessariamente de um cenário já associado
                # Vamos buscar todos os tributos que podem ter PIS
                pass
            elif tipo_tributo == 'cofins':
                # Para COFINS, não precisamos necessariamente de um cenário já associado
                # Vamos buscar todos os tributos que podem ter COFINS
                pass
            elif tipo_tributo == 'difal':
                # Para DIFAL, não precisamos necessariamente de um cenário já associado
                # Vamos buscar todos os tributos que podem ter DIFAL
                pass

            # Buscar tributos
            tributos = query.all()

            if not tributos:
                return jsonify({
                    'success': False,
                    'message': f'Nenhum tributo de {tipo_tributo.upper()} encontrado para a direção {direcao}'
                }), 404

            # Obter IDs dos tributos
            tributo_ids = [tributo.id for tributo in tributos]

            # Capturar o contexto da aplicação antes de criar a thread
            app = current_app._get_current_object()

            def process_audit_in_background():
                """Processa auditoria em background"""
                try:
                    # Executar dentro do contexto da aplicação
                    with app.app_context():
                        logger.info(f"[AUDIT] Processando auditoria em background para {len(tributo_ids)} tributos do tipo {tipo_tributo} na direção {direcao}")

                        # Inicializar serviço de auditoria com callback
                        auditoria_service = AuditoriaService(empresa_id, usuario_id, progress_callback)

                        # Executar auditoria
                        resultado = auditoria_service.executar_auditoria(
                            tributo_ids=tributo_ids,
                            forcar_recalculo=forcar_recalculo,
                            tipo_tributo=tipo_tributo
                        )

                        # Enviar notificação de conclusão via WebSocket
                        if websocket_service:
                            websocket_service.send_audit_complete(audit_id, {
                                "message": "Auditoria concluída com sucesso",
                                "results": resultado
                            })

                except Exception as e:
                    logger.error(f"[AUDIT] Erro no processamento em background: {str(e)}")
                    # Enviar notificação de erro via WebSocket
                    if websocket_service:
                        websocket_service.send_audit_error(audit_id, {
                            "message": f"Erro na auditoria: {str(e)}"
                        })

            # Iniciar processamento em background
            thread = threading.Thread(target=process_audit_in_background)
            thread.daemon = True
            thread.start()

            # Retornar resposta imediata
            return jsonify({
                'success': True,
                'audit_id': audit_id,
                'message': 'Auditoria iniciada',
                'status': 'processing',
                'total_tributos': len(tributo_ids)
            }), 202

        except Exception as e:
            logger.error(f"Erro ao executar auditoria de tributos: {str(e)}")
            return jsonify({
                'success': False,
                'message': f'Erro ao executar auditoria de tributos: {str(e)}'
            }), 500

    # Se for uma requisição GET, listar os tributos auditados
    """
    Lista os tributos auditados
    """
    try:
        # Obter parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)
        tipo_tributo = request.args.get('tipo_tributo')  # icms, icms_st, ipi, pis, cofins, difal
        auditoria_status = request.args.get('auditoria_status')  # pendente, realizada
        direcao = request.args.get('direcao')  # entrada, saida
        cliente_id = request.args.get('cliente_id', type=int)
        produto_id = request.args.get('produto_id', type=int)
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Validar parâmetros
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        # Construir query base
        query = Tributo.query.filter_by(empresa_id=empresa_id)

        # Aplicar filtros
        if tipo_tributo:
            # Filtrar por tipo de tributo
            if tipo_tributo == 'icms':
                query = query.filter(Tributo.cenario_icms_id != None)
            elif tipo_tributo == 'icms_st':
                query = query.filter(Tributo.cenario_icms_st_id != None)
            elif tipo_tributo == 'ipi':
                query = query.filter(Tributo.cenario_ipi_id != None)
            elif tipo_tributo == 'pis':
                query = query.filter(Tributo.cenario_pis_id != None)
            elif tipo_tributo == 'cofins':
                query = query.filter(Tributo.cenario_cofins_id != None)
            elif tipo_tributo == 'difal':
                query = query.filter(Tributo.cenario_difal_id != None)

        if auditoria_status:
            query = query.filter_by(auditoria_status=auditoria_status)

        if direcao:
            if direcao == 'entrada':
                query = query.filter_by(tipo_operacao='0')
            elif direcao == 'saida':
                query = query.filter_by(tipo_operacao='1')

        if cliente_id:
            query = query.filter_by(cliente_id=cliente_id)

        if produto_id:
            query = query.filter_by(produto_id=produto_id)

        if year:
            query = query.filter(db.extract('year', Tributo.data_emissao) == year)

        if month:
            query = query.filter(db.extract('month', Tributo.data_emissao) == month)

        # Executar query
        tributos = query.all()

        # Formatar resultado
        result = []
        for tributo in tributos:
            # Adicionar informações do cliente e produto
            cliente = db.session.get(Cliente, tributo.cliente_id)
            produto = db.session.get(Produto, tributo.produto_id)

            tributo_dict = tributo.to_dict()
            tributo_dict['cliente'] = cliente.to_dict() if cliente else None
            tributo_dict['produto'] = produto.to_dict() if produto else None

            result.append(tributo_dict)

        return jsonify({
            "success": True,
            "tributos": result
        }), 200

    except Exception as e:
        logger.error(f"Erro ao listar tributos auditados: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao listar tributos auditados: {str(e)}'
        }), 500

@auditoria_bp.route('/api/auditoria/tributos/<int:tributo_id>', methods=['GET'])
@jwt_required()
def obter_tributo_auditado(tributo_id):
    """
    Obtém os detalhes de um tributo auditado
    """
    try:
        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar o tributo
        tributo = db.session.get(Tributo, tributo_id)

        if not tributo:
            return jsonify({"message": "Tributo não encontrado"}), 404

        # Adicionar informações do cliente e produto
        cliente = db.session.get(Cliente, tributo.cliente_id)
        produto = db.session.get(Produto, tributo.produto_id)

        tributo_dict = tributo.to_dict()
        tributo_dict['cliente'] = cliente.to_dict() if cliente else None
        tributo_dict['produto'] = produto.to_dict() if produto else None

        # Adicionar informações dos cenários utilizados na auditoria
        if tributo.cenario_icms_id:
            from models import CenarioICMS
            cenario_icms = db.session.get(CenarioICMS, tributo.cenario_icms_id)
            tributo_dict['cenario_icms'] = cenario_icms.to_dict() if cenario_icms else None

        if tributo.cenario_icms_st_id:
            from models import CenarioICMSST
            cenario_icms_st = db.session.get(CenarioICMSST, tributo.cenario_icms_st_id)
            tributo_dict['cenario_icms_st'] = cenario_icms_st.to_dict() if cenario_icms_st else None

        if tributo.cenario_ipi_id:
            from models import CenarioIPI
            cenario_ipi = db.session.get(CenarioIPI, tributo.cenario_ipi_id)
            tributo_dict['cenario_ipi'] = cenario_ipi.to_dict() if cenario_ipi else None

        if tributo.cenario_pis_id:
            from models import CenarioPIS
            cenario_pis = db.session.get(CenarioPIS, tributo.cenario_pis_id)
            tributo_dict['cenario_pis'] = cenario_pis.to_dict() if cenario_pis else None

        if tributo.cenario_cofins_id:
            from models import CenarioCOFINS
            cenario_cofins = db.session.get(CenarioCOFINS, tributo.cenario_cofins_id)
            tributo_dict['cenario_cofins'] = cenario_cofins.to_dict() if cenario_cofins else None

        if tributo.cenario_difal_id:
            from models import CenarioDIFAL
            cenario_difal = db.session.get(CenarioDIFAL, tributo.cenario_difal_id)
            tributo_dict['cenario_difal'] = cenario_difal.to_dict() if cenario_difal else None

        return jsonify({
            "success": True,
            "tributo": tributo_dict
        }), 200

    except Exception as e:
        logger.error(f"Erro ao obter tributo auditado: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao obter tributo auditado: {str(e)}'
        }), 500

@auditoria_bp.route('/api/auditoria/debug/tributos', methods=['GET'])
@jwt_required()
def debug_tributos():
    """
    Endpoint de debug para verificar tributos existentes
    """
    try:
        # Obter parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)
        direcao = request.args.get('direcao')

        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 404

        # Construir query base
        query = Tributo.query.filter_by(empresa_id=empresa_id)

        # Filtrar por direção (entrada/saída)
        if direcao:
            if direcao == 'entrada':
                query = query.filter_by(tipo_operacao='0')
            elif direcao == 'saida':
                query = query.filter_by(tipo_operacao='1')

        # Buscar tributos
        tributos = query.all()

        # Formatar resultado
        result = []
        for tributo in tributos:
            tributo_dict = {
                'id': tributo.id,
                'empresa_id': tributo.empresa_id,
                'cliente_id': tributo.cliente_id,
                'produto_id': tributo.produto_id,
                'tipo_operacao': tributo.tipo_operacao,
                'data_emissao': tributo.data_emissao.isoformat() if tributo.data_emissao else None,
                'data_saida': tributo.data_saida.isoformat() if tributo.data_saida else None,
                'valor_total': tributo.valor_total,
                'auditoria_status': tributo.auditoria_status,
                'auditoria_data': tributo.auditoria_data.isoformat() if tributo.auditoria_data else None
            }
            result.append(tributo_dict)

        return jsonify({
            'success': True,
            'count': len(tributos),
            'tributos': result
        })

    except Exception as e:
        logger.error(f"Erro ao buscar tributos para debug: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao buscar tributos para debug: {str(e)}'
        }), 500

@auditoria_bp.route('/api/auditoria/resultados', methods=['GET'])
@jwt_required()
def listar_resultados_auditoria():
    """
    Lista os resultados da auditoria
    """
    try:
        # Obter parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)
        tipo_tributo = request.args.get('tipo_tributo')  # icms, icms_st, ipi, pis, cofins, difal
        status = request.args.get('status')  # conforme, inconsistente
        tributo_id = request.args.get('tributo_id', type=int)
        nota_fiscal_item_id = request.args.get('nota_fiscal_item_id', type=int)
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Validar parâmetros
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        # Construir query base
        query = AuditoriaResultado.query.filter_by(empresa_id=empresa_id)

        # Aplicar filtros
        if tipo_tributo:
            query = query.filter_by(tipo_tributo=tipo_tributo)

        if status:
            query = query.filter_by(status=status)

        if tributo_id:
            query = query.filter_by(tributo_id=tributo_id)

        if nota_fiscal_item_id:
            query = query.filter_by(nota_fiscal_item_id=nota_fiscal_item_id)

        # Filtrar por data
        if year or month:
            # Juntar com a tabela de tributos para filtrar por data
            query = query.join(Tributo, AuditoriaResultado.tributo_id == Tributo.id)

            if year:
                query = query.filter(db.extract('year', Tributo.data_emissao) == year)

            if month:
                query = query.filter(db.extract('month', Tributo.data_emissao) == month)

        # Executar query
        resultados = query.all()

        # Formatar resultado
        result = []
        for resultado in resultados:
            # Adicionar informações do tributo, cliente e produto
            tributo = db.session.get(Tributo, resultado.tributo_id)
            nota_fiscal_item = db.session.get(NotaFiscalItem, resultado.nota_fiscal_item_id)
            cliente = db.session.get(Cliente, tributo.cliente_id) if tributo else None
            produto = db.session.get(Produto, tributo.produto_id) if tributo else None

            resultado_dict = resultado.to_dict()
            resultado_dict['tributo'] = tributo.to_dict() if tributo else None
            resultado_dict['nota_fiscal_item'] = nota_fiscal_item.to_dict() if nota_fiscal_item else None
            resultado_dict['cliente'] = cliente.to_dict() if cliente else None
            resultado_dict['produto'] = produto.to_dict() if produto else None

            result.append(resultado_dict)

        return jsonify({
            "success": True,
            "resultados": result
        }), 200

    except Exception as e:
        logger.error(f"Erro ao listar resultados da auditoria: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao listar resultados da auditoria: {str(e)}'
        }), 500

@auditoria_bp.route('/api/auditoria/resultados/<int:resultado_id>', methods=['GET'])
@jwt_required()
def obter_resultado_auditoria(resultado_id):
    """
    Obtém os detalhes de um resultado de auditoria
    """
    try:
        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar o resultado
        resultado = db.session.get(AuditoriaResultado, resultado_id)

        if not resultado:
            return jsonify({"message": "Resultado de auditoria não encontrado"}), 404

        # Adicionar informações do tributo, cliente e produto
        tributo = db.session.get(Tributo, resultado.tributo_id)
        nota_fiscal_item = db.session.get(NotaFiscalItem, resultado.nota_fiscal_item_id)
        cliente = db.session.get(Cliente, tributo.cliente_id) if tributo else None
        produto = db.session.get(Produto, tributo.produto_id) if tributo else None

        resultado_dict = resultado.to_dict()
        resultado_dict['tributo'] = tributo.to_dict() if tributo else None
        resultado_dict['nota_fiscal_item'] = nota_fiscal_item.to_dict() if nota_fiscal_item else None
        resultado_dict['cliente'] = cliente.to_dict() if cliente else None
        resultado_dict['produto'] = produto.to_dict() if produto else None

        # Adicionar informações do cenário utilizado
        if resultado.tipo_tributo == 'icms' and tributo and tributo.cenario_icms_id:
            cenario = db.session.get(CenarioICMS, tributo.cenario_icms_id)
            resultado_dict['cenario'] = cenario.to_dict() if cenario else None
        elif resultado.tipo_tributo == 'icms_st' and tributo and tributo.cenario_icms_st_id:
            cenario = db.session.get(CenarioICMSST, tributo.cenario_icms_st_id)
            resultado_dict['cenario'] = cenario.to_dict() if cenario else None
        elif resultado.tipo_tributo == 'ipi' and tributo and tributo.cenario_ipi_id:
            cenario = db.session.get(CenarioIPI, tributo.cenario_ipi_id)
            resultado_dict['cenario'] = cenario.to_dict() if cenario else None
        elif resultado.tipo_tributo == 'pis' and tributo and tributo.cenario_pis_id:
            cenario = db.session.get(CenarioPIS, tributo.cenario_pis_id)
            resultado_dict['cenario'] = cenario.to_dict() if cenario else None
        elif resultado.tipo_tributo == 'cofins' and tributo and tributo.cenario_cofins_id:
            cenario = db.session.get(CenarioCOFINS, tributo.cenario_cofins_id)
            resultado_dict['cenario'] = cenario.to_dict() if cenario else None
        elif resultado.tipo_tributo == 'difal' and tributo and tributo.cenario_difal_id:
            cenario = db.session.get(CenarioDIFAL, tributo.cenario_difal_id)
            resultado_dict['cenario'] = cenario.to_dict() if cenario else None

        return jsonify({
            "success": True,
            "resultado": resultado_dict
        }), 200

    except Exception as e:
        logger.error(f"Erro ao obter resultado da auditoria: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao obter resultado da auditoria: {str(e)}'
        }), 500

@auditoria_bp.route('/api/auditoria/sumarios', methods=['GET'])
@jwt_required()
def listar_sumarios_auditoria():
    """
    Lista os sumários da auditoria
    """
    try:
        # Obter parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)
        tipo_tributo = request.args.get('tipo_tributo')  # icms, icms_st, ipi, pis, cofins, difal
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Validar parâmetros
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        # Construir query base
        query = AuditoriaSumario.query.filter_by(empresa_id=empresa_id)

        # Aplicar filtros
        if tipo_tributo:
            query = query.filter_by(tipo_tributo=tipo_tributo)

        if year:
            query = query.filter_by(ano=year)

        if month:
            query = query.filter_by(mes=month)

        # Executar query
        sumarios = query.all()

        # Formatar resultado
        result = []
        for sumario in sumarios:
            result.append(sumario.to_dict())

        return jsonify({
            "success": True,
            "sumarios": result
        }), 200

    except Exception as e:
        logger.error(f"Erro ao listar sumários da auditoria: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao listar sumários da auditoria: {str(e)}'
        }), 500

@auditoria_bp.route('/api/auditoria/dashboard', methods=['GET'])
@jwt_required()
def obter_dashboard_auditoria():
    """
    Obtém os dados para o dashboard de auditoria
    """
    try:
        # Obter parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)
        tipo_tributo = request.args.get('tipo_tributo')  # icms, icms_st, ipi, pis, cofins, difal
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Validar parâmetros
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not tipo_tributo:
            return jsonify({
                'success': False,
                'message': 'Tipo de tributo não informado'
            }), 400

        if not year:
            return jsonify({
                'success': False,
                'message': 'Ano não informado'
            }), 400

        if not month:
            return jsonify({
                'success': False,
                'message': 'Mês não informado'
            }), 400

        # Buscar o sumário de auditoria
        sumario = AuditoriaSumario.query.filter_by(
            empresa_id=empresa_id,
            tipo_tributo=tipo_tributo,
            ano=year,
            mes=month
        ).first()

        if not sumario:
            return jsonify({
                'success': False,
            }), 404

        # Buscar os resultados de auditoria inconsistentes
        resultados_inconsistentes = AuditoriaResultado.query.filter(
            AuditoriaResultado.empresa_id == empresa_id,
            AuditoriaResultado.tipo_tributo == tipo_tributo,
            AuditoriaResultado.status == 'inconsistente'
        ).join(
            Tributo, AuditoriaResultado.tributo_id == Tributo.id
        ).filter(
            db.extract('year', Tributo.data_emissao) == year,
            db.extract('month', Tributo.data_emissao) == month
        ).all()

        # Contar inconsistências vistas e não vistas
        total_inconsistentes = len(resultados_inconsistentes)
        inconsistentes_vistas = len([r for r in resultados_inconsistentes if r.analista_visualizou])
        inconsistentes_nao_vistas = total_inconsistentes - inconsistentes_vistas

        # Formatar resultados inconsistentes
        resultados_formatados = []
        for resultado in resultados_inconsistentes:
            tributo = db.session.get(Tributo, resultado.tributo_id)
            nota_fiscal_item = db.session.get(NotaFiscalItem, resultado.nota_fiscal_item_id)
            cliente = db.session.get(Cliente, tributo.cliente_id) if tributo else None
            produto = db.session.get(Produto, tributo.produto_id) if tributo else None

            # Obter o cenário utilizado
            cenario_id = resultado.cenario_id
            cenario = None
            if tipo_tributo == 'icms':
                cenario = db.session.get(CenarioICMS, cenario_id)
            elif tipo_tributo == 'icms_st':
                cenario = db.session.get(CenarioICMSST, cenario_id)
            elif tipo_tributo == 'ipi':
                cenario = db.session.get(CenarioIPI, cenario_id)
            elif tipo_tributo == 'pis':
                cenario = db.session.get(CenarioPIS, cenario_id)
            elif tipo_tributo == 'cofins':
                cenario = db.session.get(CenarioCOFINS, cenario_id)
            elif tipo_tributo == 'difal':
                cenario = db.session.get(CenarioDIFAL, cenario_id)

            # Obter alíquotas da nota e do cenário
            aliquota_nota = None
            aliquota_cenario = None

            if tributo:
                if tipo_tributo == 'icms':
                    aliquota_nota = float(tributo.icms_aliquota) if tributo.icms_aliquota else None
                elif tipo_tributo == 'icms_st':
                    aliquota_nota = float(tributo.icms_st_aliquota) if tributo.icms_st_aliquota else None
                elif tipo_tributo == 'ipi':
                    aliquota_nota = float(tributo.ipi_aliquota) if tributo.ipi_aliquota else None
                elif tipo_tributo == 'pis':
                    aliquota_nota = float(tributo.pis_aliquota) if tributo.pis_aliquota else None
                elif tipo_tributo == 'cofins':
                    aliquota_nota = float(tributo.cofins_aliquota) if tributo.cofins_aliquota else None
                elif tipo_tributo == 'difal':
                    aliquota_nota = float(tributo.difal_p_icms_uf_dest) if tributo.difal_p_icms_uf_dest else None

            if cenario:
                if hasattr(cenario, 'aliquota') and cenario.aliquota:
                    aliquota_cenario = float(cenario.aliquota)
                elif tipo_tributo == 'icms_st' and hasattr(cenario, 'icms_st_aliquota') and cenario.icms_st_aliquota:
                    aliquota_cenario = float(cenario.icms_st_aliquota)
                elif tipo_tributo == 'difal' and hasattr(cenario, 'p_icms_uf_dest') and cenario.p_icms_uf_dest:
                    aliquota_cenario = float(cenario.p_icms_uf_dest)

            # Comparar campos individuais para determinar quais são diferentes
            base_calculo_diferente = False
            aliquota_diferente = False
            valor_diferente = False

            # Tolerância para comparação de valores
            tolerancia_valor = 0.01
            tolerancia_aliquota = 0.0001

            # Comparar base de cálculo
            if resultado.base_calculo_nota is not None and resultado.base_calculo_calculada is not None:
                base_calculo_diferente = abs(float(resultado.base_calculo_nota) - float(resultado.base_calculo_calculada)) > tolerancia_valor
            elif resultado.base_calculo_nota != resultado.base_calculo_calculada:  # Um é None e o outro não
                base_calculo_diferente = True

            # Comparar alíquota
            if aliquota_nota is not None and aliquota_cenario is not None:
                aliquota_diferente = abs(float(aliquota_nota) - float(aliquota_cenario)) > tolerancia_aliquota
            elif aliquota_nota != aliquota_cenario:  # Um é None e o outro não
                aliquota_diferente = True

            # Comparar valor
            if resultado.valor_nota is not None and resultado.valor_calculado is not None:
                valor_diferente = abs(float(resultado.valor_nota) - float(resultado.valor_calculado)) > tolerancia_valor
            elif resultado.valor_nota != resultado.valor_calculado:  # Um é None e o outro não
                valor_diferente = True

            # Formatar resultado
            resultado_dict = {
                'id': resultado.id,
                'nota': {
                    'origem': 'NFe',
                    'numero': nota_fiscal_item.numero_nf if nota_fiscal_item else None,
                    'cfop': nota_fiscal_item.cfop if nota_fiscal_item else None,
                    'produto_numero': produto.codigo if produto else None,
                    'produto_descricao': produto.descricao if produto else None,
                    'ncm': nota_fiscal_item.ncm if nota_fiscal_item else None,
                    'cest': produto.cest if produto else None,
                    'cst': getattr(tributo, f"{tipo_tributo.replace('-', '_')}_cst") if tributo else None,
                    'base_calculo': float(resultado.base_calculo_nota) if resultado.base_calculo_nota else None,
                    'aliquota': aliquota_nota,
                    'valor': float(resultado.valor_nota) if resultado.valor_nota else 0,
                    'status': 'inconsistente'
                },
                'cenario': {
                    'origem': 'Cenário',
                    'numero': cenario.id if cenario else None,
                    'cfop': cenario.cfop if cenario else None,
                    'produto_numero': produto.codigo if produto else None,
                    'produto_descricao': produto.descricao if produto else None,
                    'ncm': cenario.ncm if cenario else None,
                    'cest': produto.cest if produto else None,
                    'cst': cenario.cst if cenario else None,
                    'base_calculo': float(resultado.base_calculo_calculada) if resultado.base_calculo_calculada else None,
                    'aliquota': aliquota_cenario,
                    'valor': float(resultado.valor_calculado) if resultado.valor_calculado else 0,
                    'status': 'cenário'
                },
                'diferenca': float(resultado.valor_calculado) - float(resultado.valor_nota) if resultado.valor_calculado and resultado.valor_nota else 0,
                'comparacao': {
                    'base_calculo_diferente': base_calculo_diferente,
                    'aliquota_diferente': aliquota_diferente,
                    'valor_diferente': valor_diferente
                }
            }
            resultados_formatados.append(resultado_dict)

        # Formatar resposta
        sumario_dict = sumario.to_dict()
        sumario_dict['total_inconsistentes_vistas'] = inconsistentes_vistas
        sumario_dict['total_inconsistentes_nao_vistas'] = inconsistentes_nao_vistas

        dashboard = {
            'sumario': sumario_dict,
            'resultados_inconsistentes': resultados_formatados
        }

        return jsonify({
            "success": True,
            "dashboard": dashboard
        }), 200

    except Exception as e:
        logger.error(f"Erro ao obter dashboard de auditoria: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao obter dashboard de auditoria: {str(e)}'
        }), 500


@auditoria_bp.route('/api/auditoria/dashboard/filtros', methods=['GET'])
@jwt_required()
def obter_opcoes_filtro_auditoria():
    """
    Obtém as opções para os filtros do dashboard de auditoria com relacionamentos
    """
    try:
        # Obter parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)
        tipo_tributo = request.args.get('tipo_tributo')  # icms, icms_st, ipi, pis, cofins, difal
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Validar parâmetros
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not tipo_tributo:
            return jsonify({
                'success': False,
                'message': 'Tipo de tributo não informado'
            }), 400

        # Buscar resultados de auditoria inconsistentes para extrair opções
        query = AuditoriaResultado.query.filter(
            AuditoriaResultado.empresa_id == empresa_id,
            AuditoriaResultado.tipo_tributo == tipo_tributo,
            AuditoriaResultado.status == 'inconsistente'
        ).join(
            Tributo, AuditoriaResultado.tributo_id == Tributo.id
        ).join(
            NotaFiscalItem, AuditoriaResultado.nota_fiscal_item_id == NotaFiscalItem.id
        )

        # Filtrar por período se especificado
        if year and month:
            query = query.filter(
                db.extract('year', Tributo.data_emissao) == year,
                db.extract('month', Tributo.data_emissao) == month
            )

        resultados = query.all()

        # Extrair opções únicas com relacionamentos
        cfops = {}  # {cfop: {ncms: set(), csts: set(), aliquotas: set()}}
        ncms = {}   # {ncm: {cfops: set(), csts: set(), aliquotas: set()}}
        csts = {}   # {cst: {cfops: set(), ncms: set(), aliquotas: set()}}
        aliquotas = {}  # {aliquota: {cfops: set(), ncms: set(), csts: set()}}

        for resultado in resultados:
            tributo = db.session.get(Tributo, resultado.tributo_id)
            nota_fiscal_item = db.session.get(NotaFiscalItem, resultado.nota_fiscal_item_id)

            if not tributo or not nota_fiscal_item:
                continue

            cfop = nota_fiscal_item.cfop
            ncm = nota_fiscal_item.ncm
            cst = getattr(tributo, f"{tipo_tributo.replace('-', '_')}_cst")

            # Obter alíquota baseada no tipo de tributo
            aliquota = None
            if tipo_tributo == 'icms':
                aliquota = float(tributo.icms_aliquota) if tributo.icms_aliquota else None
            elif tipo_tributo == 'icms_st':
                aliquota = float(tributo.icms_st_aliquota) if tributo.icms_st_aliquota else None
            elif tipo_tributo == 'ipi':
                aliquota = float(tributo.ipi_aliquota) if tributo.ipi_aliquota else None
            elif tipo_tributo == 'pis':
                aliquota = float(tributo.pis_aliquota) if tributo.pis_aliquota else None
            elif tipo_tributo == 'cofins':
                aliquota = float(tributo.cofins_aliquota) if tributo.cofins_aliquota else None
            elif tipo_tributo == 'difal':
                aliquota = float(tributo.difal_p_icms_uf_dest) if tributo.difal_p_icms_uf_dest else None

            # Construir relacionamentos
            if cfop:
                if cfop not in cfops:
                    cfops[cfop] = {'ncms': set(), 'csts': set(), 'aliquotas': set()}
                if ncm:
                    cfops[cfop]['ncms'].add(ncm)
                if cst:
                    cfops[cfop]['csts'].add(cst)
                if aliquota is not None:
                    cfops[cfop]['aliquotas'].add(str(aliquota))

            if ncm:
                if ncm not in ncms:
                    ncms[ncm] = {'cfops': set(), 'csts': set(), 'aliquotas': set()}
                if cfop:
                    ncms[ncm]['cfops'].add(cfop)
                if cst:
                    ncms[ncm]['csts'].add(cst)
                if aliquota is not None:
                    ncms[ncm]['aliquotas'].add(str(aliquota))

            if cst:
                if cst not in csts:
                    csts[cst] = {'cfops': set(), 'ncms': set(), 'aliquotas': set()}
                if cfop:
                    csts[cst]['cfops'].add(cfop)
                if ncm:
                    csts[cst]['ncms'].add(ncm)
                if aliquota is not None:
                    csts[cst]['aliquotas'].add(str(aliquota))

            if aliquota is not None:
                aliquota_str = str(aliquota)
                if aliquota_str not in aliquotas:
                    aliquotas[aliquota_str] = {'cfops': set(), 'ncms': set(), 'csts': set()}
                if cfop:
                    aliquotas[aliquota_str]['cfops'].add(cfop)
                if ncm:
                    aliquotas[aliquota_str]['ncms'].add(ncm)
                if cst:
                    aliquotas[aliquota_str]['csts'].add(cst)

        # Converter sets para listas e formatar resposta
        opcoes = {
            'cfops': [{'value': cfop, 'related': {
                'ncms': list(data['ncms']),
                'csts': list(data['csts']),
                'aliquotas': list(data['aliquotas'])
            }} for cfop, data in sorted(cfops.items())],

            'ncms': [{'value': ncm, 'related': {
                'cfops': list(data['cfops']),
                'csts': list(data['csts']),
                'aliquotas': list(data['aliquotas'])
            }} for ncm, data in sorted(ncms.items())],

            'csts': [{'value': cst, 'related': {
                'cfops': list(data['cfops']),
                'ncms': list(data['ncms']),
                'aliquotas': list(data['aliquotas'])
            }} for cst, data in sorted(csts.items())],

            'aliquotas': [{'value': aliquota, 'related': {
                'cfops': list(data['cfops']),
                'ncms': list(data['ncms']),
                'csts': list(data['csts'])
            }} for aliquota, data in sorted(aliquotas.items(), key=lambda x: float(x[0]))]
        }

        return jsonify({
            "success": True,
            "opcoes": opcoes
        }), 200

    except Exception as e:
        logger.error(f"Erro ao obter opções de filtro de auditoria: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao obter opções de filtro de auditoria: {str(e)}'
        }), 500


@auditoria_bp.route('/api/auditoria/dashboard/detalhamento', methods=['GET'])
@jwt_required()
def obter_detalhamento_auditoria():
    """
    Obtém os dados detalhados para a tab de detalhamento do dashboard de auditoria
    """
    try:
        # Obter parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)
        tipo_tributo = request.args.get('tipo_tributo')  # icms, icms_st, ipi, pis, cofins, difal
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        status = request.args.get('status')  # conforme, inconsistente, todos

        # Filtros específicos - suportar múltiplos valores
        origem = request.args.get('origem')
        numero = request.args.get('numero')
        cfops = request.args.getlist('cfop') if request.args.getlist('cfop') else []  # Múltiplos CFOPs
        produto_numero = request.args.get('produto_numero')
        ncms = request.args.getlist('ncm') if request.args.getlist('ncm') else []  # Múltiplos NCMs
        csts = request.args.getlist('cst') if request.args.getlist('cst') else []  # Múltiplos CSTs
        aliquotas = request.args.getlist('aliquota') if request.args.getlist('aliquota') else []  # Múltiplas alíquotas
        atividade = request.args.get('atividade')
        destinacao = request.args.get('destinacao')
        analista_visualizou = request.args.get('analista_visualizou')  # 'true', 'false', ou None para todos



        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Validar parâmetros
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not tipo_tributo:
            return jsonify({
                'success': False,
                'message': 'Tipo de tributo não informado'
            }), 400

        # Buscar resultados de auditoria - aplicar filtro de status primeiro para performance
        query = AuditoriaResultado.query.filter(
            AuditoriaResultado.empresa_id == empresa_id,
            AuditoriaResultado.tipo_tributo == tipo_tributo
        )

        # Filtrar por status primeiro (mais eficiente para inconsistências)
        if status and status != 'todos':
            query = query.filter(AuditoriaResultado.status == status)

        query = query.join(
            Tributo, AuditoriaResultado.tributo_id == Tributo.id
        ).join(
            Cliente, Tributo.cliente_id == Cliente.id
        )

        # Filtrar por período se especificado
        if year and month:
            query = query.filter(
                db.extract('year', Tributo.data_emissao) == year,
                db.extract('month', Tributo.data_emissao) == month
            )

        # Filtrar por atividade se especificado
        if atividade:
            query = query.filter(Cliente.atividade.ilike(f'%{atividade}%'))

        # Filtrar por destinação se especificado
        if destinacao:
            query = query.filter(Cliente.destinacao.ilike(f'%{destinacao}%'))

        # Filtrar por análise do analista se especificado
        if analista_visualizou is not None:
            if analista_visualizou.lower() == 'true':
                query = query.filter(AuditoriaResultado.analista_visualizou == True)
            elif analista_visualizou.lower() == 'false':
                query = query.filter(AuditoriaResultado.analista_visualizou == False)

        resultados = query.all()

        # Formatar resultados detalhados
        resultados_formatados = []
        for resultado in resultados:
            tributo = db.session.get(Tributo, resultado.tributo_id)
            nota_fiscal_item = db.session.get(NotaFiscalItem, resultado.nota_fiscal_item_id)
            cliente = db.session.get(Cliente, tributo.cliente_id) if tributo else None
            produto = db.session.get(Produto, tributo.produto_id) if tributo else None

            # Obter o cenário utilizado
            cenario_id = resultado.cenario_id
            cenario = None
            if tipo_tributo == 'icms':
                cenario = db.session.get(CenarioICMS, cenario_id)
            elif tipo_tributo == 'icms_st':
                cenario = db.session.get(CenarioICMSST, cenario_id)
            elif tipo_tributo == 'ipi':
                cenario = db.session.get(CenarioIPI, cenario_id)
            elif tipo_tributo == 'pis':
                cenario = db.session.get(CenarioPIS, cenario_id)
            elif tipo_tributo == 'cofins':
                cenario = db.session.get(CenarioCOFINS, cenario_id)
            elif tipo_tributo == 'difal':
                cenario = db.session.get(CenarioDIFAL, cenario_id)

            # Obter alíquotas da nota e do cenário
            aliquota_nota = None
            aliquota_cenario = None

            if tributo:
                if tipo_tributo == 'icms':
                    aliquota_nota = float(tributo.icms_aliquota) if tributo.icms_aliquota else None
                elif tipo_tributo == 'icms_st':
                    aliquota_nota = float(tributo.icms_st_aliquota) if tributo.icms_st_aliquota else None
                elif tipo_tributo == 'ipi':
                    aliquota_nota = float(tributo.ipi_aliquota) if tributo.ipi_aliquota else None
                elif tipo_tributo == 'pis':
                    aliquota_nota = float(tributo.pis_aliquota) if tributo.pis_aliquota else None
                elif tipo_tributo == 'cofins':
                    aliquota_nota = float(tributo.cofins_aliquota) if tributo.cofins_aliquota else None
                elif tipo_tributo == 'difal':
                    aliquota_nota = float(tributo.difal_p_icms_uf_dest) if tributo.difal_p_icms_uf_dest else None

            if cenario:
                if hasattr(cenario, 'aliquota') and cenario.aliquota:
                    aliquota_cenario = float(cenario.aliquota)
                elif tipo_tributo == 'icms_st' and hasattr(cenario, 'icms_st_aliquota') and cenario.icms_st_aliquota:
                    aliquota_cenario = float(cenario.icms_st_aliquota)
                elif tipo_tributo == 'difal' and hasattr(cenario, 'p_icms_uf_dest') and cenario.p_icms_uf_dest:
                    aliquota_cenario = float(cenario.p_icms_uf_dest)

            # Aplicar filtros específicos
            numero_nf = nota_fiscal_item.numero_nf if nota_fiscal_item else None
            cfop_item = nota_fiscal_item.cfop if nota_fiscal_item else None
            produto_codigo = produto.codigo if produto else None
            ncm_item = nota_fiscal_item.ncm if nota_fiscal_item else None
            cst_item = getattr(tributo, f"{tipo_tributo.replace('-', '_')}_cst") if tributo else None

            # Aplicar filtros se especificados
            if origem and origem.lower() not in ['nfe', 'nota']:
                continue
            if numero and numero_nf and numero.lower() not in numero_nf.lower():
                continue
            if cfops and len(cfops) > 0 and cfop_item and cfop_item not in cfops:
                continue
            if produto_numero and produto_codigo and produto_numero.lower() not in produto_codigo.lower():
                continue
            if ncms and len(ncms) > 0 and ncm_item and ncm_item not in ncms:
                continue
            if csts and len(csts) > 0 and cst_item and cst_item not in csts:
                continue

            # Filtrar por alíquota se especificado
            if aliquotas and len(aliquotas) > 0 and aliquota_nota is not None:
                aliquota_str = str(float(aliquota_nota))
                if aliquota_str not in aliquotas:
                    continue

            # Comparar campos individuais para determinar quais são diferentes
            base_calculo_diferente = False
            aliquota_diferente = False
            valor_diferente = False

            # Tolerância para comparação de valores
            tolerancia_valor = 0.01
            tolerancia_aliquota = 0.0001

            # Comparar base de cálculo
            if resultado.base_calculo_nota is not None and resultado.base_calculo_calculada is not None:
                base_calculo_diferente = abs(float(resultado.base_calculo_nota) - float(resultado.base_calculo_calculada)) > tolerancia_valor
            elif resultado.base_calculo_nota != resultado.base_calculo_calculada:  # Um é None e o outro não
                base_calculo_diferente = True

            # Comparar alíquota
            if aliquota_nota is not None and aliquota_cenario is not None:
                aliquota_diferente = abs(float(aliquota_nota) - float(aliquota_cenario)) > tolerancia_aliquota
            elif aliquota_nota != aliquota_cenario:  # Um é None e o outro não
                aliquota_diferente = True

            # Comparar valor
            if resultado.valor_nota is not None and resultado.valor_calculado is not None:
                valor_diferente = abs(float(resultado.valor_nota) - float(resultado.valor_calculado)) > tolerancia_valor
            elif resultado.valor_nota != resultado.valor_calculado:  # Um é None e o outro não
                valor_diferente = True

            # Formatar resultado
            resultado_dict = {
                'id': resultado.id,
                'origem': 'NFe',
                'numero': numero_nf,
                'cfop': cfop_item,
                'produto_numero': produto_codigo,
                'produto_descricao': produto.descricao if produto else None,
                'ncm': ncm_item,
                'cest': produto.cest if produto else None,
                'cst': cst_item,
                'base_calculo': float(resultado.base_calculo_nota) if resultado.base_calculo_nota else None,
                'aliquota': aliquota_nota,
                'valor': float(resultado.valor_nota) if resultado.valor_nota else 0,
                'status': resultado.status,
                'cenario_status': resultado.cenario_status if hasattr(resultado, 'cenario_status') else 'producao',
                'data_auditoria': resultado.data_auditoria.isoformat() if resultado.data_auditoria else None,
                'analista_visualizou': resultado.analista_visualizou,
                'observacoes_analista': resultado.observacoes_analista,
                'data_visualizacao': resultado.data_visualizacao.isoformat() if resultado.data_visualizacao else None,
                'usuario_analista_id': resultado.usuario_analista_id,
                'cenario': {
                    'id': cenario.id if cenario else None,
                    'origem': 'Cenário',
                    'numero': cenario.id if cenario else None,
                    'cfop': cfop_item,
                    'produto_numero': produto_codigo,
                    'produto_descricao': produto.descricao if produto else None,
                    'ncm': ncm_item,
                    'cest': produto.cest if produto else None,
                    'cst': cenario.cst if cenario else None,  # CST do cenário, não da nota
                    'base_calculo': float(resultado.base_calculo_calculada) if resultado.base_calculo_calculada else None,
                    'aliquota': aliquota_cenario,
                    'valor': float(resultado.valor_calculado) if resultado.valor_calculado else 0,
                    'status': 'cenario'
                },
                'diferenca': float(resultado.valor_calculado) - float(resultado.valor_nota) if resultado.valor_calculado and resultado.valor_nota else 0,
                'comparacao': {
                    'base_calculo_diferente': base_calculo_diferente,
                    'aliquota_diferente': aliquota_diferente,
                    'valor_diferente': valor_diferente
                }
            }
            resultados_formatados.append(resultado_dict)

        return jsonify({
            "success": True,
            "resultados": resultados_formatados,
            "total": len(resultados_formatados)
        }), 200

    except Exception as e:
        logger.error(f"Erro ao obter detalhamento de auditoria: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao obter detalhamento de auditoria: {str(e)}'
        }), 500


@auditoria_bp.route('/api/auditoria/detalhes/<tipo>/<int:id>', methods=['GET'])
@jwt_required()
def obter_detalhes_auditoria(tipo, id):
    """
    Obtém detalhes de uma nota ou cenário para o modal
    """
    try:
        empresa_id = request.args.get('empresa_id', type=int)
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        detalhes = {}

        if tipo == 'nota':
            # Buscar dados da nota fiscal
            resultado = db.session.get(AuditoriaResultado, id)
            if not resultado:
                return jsonify({
                    'success': False,
                    'message': 'Resultado de auditoria não encontrado'
                }), 404

            tributo = db.session.get(Tributo, resultado.tributo_id)
            nota_fiscal_item = db.session.get(NotaFiscalItem, resultado.nota_fiscal_item_id)
            cliente = db.session.get(Cliente, tributo.cliente_id) if tributo else None
            produto = db.session.get(Produto, tributo.produto_id) if tributo else None

            detalhes = {
                'cliente': {
                    'razao_social': cliente.razao_social if cliente else None,
                    'cnpj': cliente.cnpj if cliente else None,
                    'inscricao_estadual': cliente.inscricao_estadual if cliente else None,
                    'municipio': cliente.municipio if cliente else None,
                    'uf': cliente.uf if cliente else None,
                    'atividade': cliente.atividade if cliente else None,
                    'destinacao': cliente.destinacao if cliente else None
                },
                'produto': {
                    'codigo': produto.codigo if produto else None,
                    'descricao': produto.descricao if produto else None,
                    'cest': produto.cest if produto else None
                },
                'nota_fiscal_item': {
                    'ncm': nota_fiscal_item.ncm if nota_fiscal_item else None,
                    'cfop': nota_fiscal_item.cfop if nota_fiscal_item else None
                },
                'tributo': {
                    'cst': getattr(tributo, f"{resultado.tipo_tributo.replace('-', '_')}_cst") if tributo else None,
                    'base_calculo': float(resultado.base_calculo_nota) if resultado.base_calculo_nota else None,
                    'aliquota': _obter_aliquota_tributo(tributo, resultado.tipo_tributo) if tributo else None,
                    'valor': float(resultado.valor_nota) if resultado.valor_nota else None,
                    'data_emissao': tributo.data_emissao.isoformat() if tributo and tributo.data_emissao else None
                }
            }

        elif tipo == 'cenario':
            # Buscar dados do cenário
            resultado = db.session.get(AuditoriaResultado, id)
            if not resultado:
                return jsonify({
                    'success': False,
                    'message': 'Resultado de auditoria não encontrado'
                }), 404

            # Obter o cenário baseado no tipo de tributo
            cenario = None
            if resultado.tipo_tributo == 'icms':
                cenario = db.session.get(CenarioICMS, resultado.cenario_id)
            elif resultado.tipo_tributo == 'icms_st':
                cenario = db.session.get(CenarioICMSST, resultado.cenario_id)
            elif resultado.tipo_tributo == 'ipi':
                cenario = db.session.get(CenarioIPI, resultado.cenario_id)
            elif resultado.tipo_tributo == 'pis':
                cenario = db.session.get(CenarioPIS, resultado.cenario_id)
            elif resultado.tipo_tributo == 'cofins':
                cenario = db.session.get(CenarioCOFINS, resultado.cenario_id)
            elif resultado.tipo_tributo == 'difal':
                cenario = db.session.get(CenarioDIFAL, resultado.cenario_id)

            if not cenario:
                return jsonify({
                    'success': False,
                    'message': 'Cenário não encontrado'
                }), 404

            cliente = db.session.get(Cliente, cenario.cliente_id)
            produto = db.session.get(Produto, cenario.produto_id)

            detalhes = {
                'cliente': {
                    'razao_social': cliente.razao_social if cliente else None,
                    'cnpj': cliente.cnpj if cliente else None,
                    'inscricao_estadual': cliente.inscricao_estadual if cliente else None,
                    'municipio': cliente.municipio if cliente else None,
                    'uf': cliente.uf if cliente else None,
                    'atividade': cliente.atividade if cliente else None,
                    'destinacao': cliente.destinacao if cliente else None
                },
                'produto': {
                    'codigo': produto.codigo if produto else None,
                    'descricao': produto.descricao if produto else None,
                    'cest': produto.cest if produto else None
                },
                'cenario': {
                    'id': cenario.id,
                    'status': cenario.status,
                    'ncm': cenario.ncm,
                    'cfop': cenario.cfop,
                    'cst': cenario.cst,
                    'aliquota': float(cenario.aliquota) if hasattr(cenario, 'aliquota') and cenario.aliquota else None,
                    'validade_inicio': cenario.validade_inicio.isoformat() if cenario.validade_inicio else None,
                    'validade_fim': cenario.validade_fim.isoformat() if cenario.validade_fim else None
                }
            }

        else:
            return jsonify({
                'success': False,
                'message': 'Tipo inválido. Use "nota" ou "cenario"'
            }), 400

        return jsonify({
            "success": True,
            "detalhes": detalhes
        }), 200

    except Exception as e:
        logger.error(f"Erro ao obter detalhes: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao obter detalhes: {str(e)}'
        }), 500

@auditoria_bp.route('/api/auditoria/relatorio/<int:id>', methods=['GET'])
@jwt_required()
def gerar_relatorio_inconsistencia(id):
    """
    Gera relatório PDF para uma inconsistência específica
    """
    try:
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from io import BytesIO

        empresa_id = request.args.get('empresa_id', type=int)
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        # Buscar dados da inconsistência
        resultado = db.session.get(AuditoriaResultado, id)
        if not resultado:
            return jsonify({
                'success': False,
                'message': 'Resultado de auditoria não encontrado'
            }), 404

        tributo = db.session.get(Tributo, resultado.tributo_id)
        nota_fiscal_item = db.session.get(NotaFiscalItem, resultado.nota_fiscal_item_id)
        cliente = db.session.get(Cliente, tributo.cliente_id) if tributo else None
        produto = db.session.get(Produto, tributo.produto_id) if tributo else None

        # Obter cenário utilizado
        cenario = None
        if resultado.tipo_tributo == 'icms':
            cenario = db.session.get(CenarioICMS, resultado.cenario_id)
        elif resultado.tipo_tributo == 'icms_st':
            cenario = db.session.get(CenarioICMSST, resultado.cenario_id)
        elif resultado.tipo_tributo == 'ipi':
            cenario = db.session.get(CenarioIPI, resultado.cenario_id)
        elif resultado.tipo_tributo == 'pis':
            cenario = db.session.get(CenarioPIS, resultado.cenario_id)
        elif resultado.tipo_tributo == 'cofins':
            cenario = db.session.get(CenarioCOFINS, resultado.cenario_id)
        elif resultado.tipo_tributo == 'difal':
            cenario = db.session.get(CenarioDIFAL, resultado.cenario_id)

        # Criar PDF
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []

        # Título
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=16,
            spaceAfter=30,
            alignment=1  # Center
        )
        story.append(Paragraph("RELATÓRIO DE INCONSISTÊNCIA FISCAL", title_style))
        story.append(Spacer(1, 20))

        # Informações do cliente
        story.append(Paragraph("DADOS DO CLIENTE", styles['Heading2']))
        cliente_data = [
            ['Razão Social:', cliente.razao_social if cliente else '-'],
            ['CNPJ:', cliente.cnpj if cliente else '-'],
            ['Inscrição Estadual:', cliente.inscricao_estadual if cliente else '-'],
            ['Município/UF:', f"{cliente.municipio}/{cliente.uf}" if cliente else '-']
        ]
        cliente_table = Table(cliente_data, colWidths=[2*inch, 4*inch])
        cliente_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (1, 0), (1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(cliente_table)
        story.append(Spacer(1, 20))

        # Informações do produto
        story.append(Paragraph("DADOS DO PRODUTO", styles['Heading2']))
        produto_data = [
            ['Código:', produto.codigo if produto else '-'],
            ['Descrição:', produto.descricao if produto else '-'],
            ['NCM:', nota_fiscal_item.ncm if nota_fiscal_item else '-'],
            ['CFOP:', nota_fiscal_item.cfop if nota_fiscal_item else '-'],
            ['CEST:', produto.cest if produto else '-']
        ]
        produto_table = Table(produto_data, colWidths=[2*inch, 4*inch])
        produto_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (1, 0), (1, -1), colors.white),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(produto_table)
        story.append(Spacer(1, 20))

        # Comparação de valores
        story.append(Paragraph("COMPARAÇÃO DE VALORES", styles['Heading2']))

        aliquota_nota = _obter_aliquota_tributo(tributo, resultado.tipo_tributo) if tributo else None
        aliquota_cenario = float(cenario.aliquota) if hasattr(cenario, 'aliquota') and cenario.aliquota else None

        comparacao_data = [
            ['', 'Nota Fiscal', 'Cenário Aplicado'],
            ['CST:', getattr(tributo, f"{resultado.tipo_tributo.replace('-', '_')}_cst") if tributo else '-',
             cenario.cst if cenario else '-'],
            ['Base de Cálculo:', f"R$ {resultado.base_calculo_nota:,.2f}" if resultado.base_calculo_nota else '-',
             f"R$ {resultado.base_calculo_calculada:,.2f}" if resultado.base_calculo_calculada else '-'],
            ['Alíquota:', f"{aliquota_nota:.2f}%" if aliquota_nota else '-',
             f"{aliquota_cenario:.2f}%" if aliquota_cenario else '-'],
            ['Valor do Tributo:', f"R$ {resultado.valor_nota:,.2f}" if resultado.valor_nota else '-',
             f"R$ {resultado.valor_calculado:,.2f}" if resultado.valor_calculado else '-']
        ]

        comparacao_table = Table(comparacao_data, colWidths=[2*inch, 2*inch, 2*inch])
        comparacao_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (1, 1), (1, -1), colors.mistyrose),
            ('BACKGROUND', (2, 1), (2, -1), colors.lightgreen),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(comparacao_table)
        story.append(Spacer(1, 20))

        # Diferença
        diferenca = float(resultado.valor_calculado) - float(resultado.valor_nota) if resultado.valor_calculado and resultado.valor_nota else 0
        diferenca_text = f"DIFERENÇA ENCONTRADA: R$ {abs(diferenca):,.2f}"
        if diferenca > 0:
            diferenca_text += " (Valor a maior na nota)"
        elif diferenca < 0:
            diferenca_text += " (Valor a menor na nota)"

        diferenca_style = ParagraphStyle(
            'Diferenca',
            parent=styles['Normal'],
            fontSize=12,
            textColor=colors.red,
            alignment=1,
            spaceAfter=20
        )
        story.append(Paragraph(diferenca_text, diferenca_style))

        # Observações
        story.append(Paragraph("OBSERVAÇÕES", styles['Heading2']))
        obs_text = f"""
        Esta inconsistência foi identificada através da auditoria fiscal automatizada do sistema.
        O cálculo foi realizado com base no cenário ID {cenario.id if cenario else 'N/A'}
        configurado para este cliente/produto.

        Recomenda-se a revisão dos dados fiscais e eventual correção da nota fiscal.

        Data da auditoria: {resultado.data_auditoria.strftime('%d/%m/%Y %H:%M') if resultado.data_auditoria else 'N/A'}
        """
        story.append(Paragraph(obs_text, styles['Normal']))

        # Gerar PDF
        doc.build(story)
        buffer.seek(0)

        from flask import Response
        return Response(
            buffer.getvalue(),
            mimetype='application/pdf',
            headers={
                'Content-Disposition': f'attachment; filename=relatorio_inconsistencia_{id}.pdf'
            }
        )

    except ImportError:
        return jsonify({
            'success': False,
            'message': 'Biblioteca reportlab não instalada. Execute: pip install reportlab'
        }), 500
    except Exception as e:
        logger.error(f"Erro ao gerar relatório: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao gerar relatório: {str(e)}'
        }), 500


def _obter_aliquota_tributo(tributo, tipo_tributo):
    """Helper para obter alíquota do tributo baseado no tipo"""
    if tipo_tributo == 'icms':
        return float(tributo.icms_aliquota) if tributo.icms_aliquota else None
    elif tipo_tributo == 'icms_st':
        return float(tributo.icms_st_aliquota) if tributo.icms_st_aliquota else None
    elif tipo_tributo == 'ipi':
        return float(tributo.ipi_aliquota) if tributo.ipi_aliquota else None
    elif tipo_tributo == 'pis':
        return float(tributo.pis_aliquota) if tributo.pis_aliquota else None
    elif tipo_tributo == 'cofins':
        return float(tributo.cofins_aliquota) if tributo.cofins_aliquota else None
    elif tipo_tributo == 'difal':
        return float(tributo.difal_p_icms_uf_dest) if tributo.difal_p_icms_uf_dest else None
    return None

@auditoria_bp.route('/api/auditoria/cenarios/<int:cenario_id>', methods=['POST'])
@jwt_required()
def executar_auditoria_por_cenario(cenario_id):
    """
    Executa a auditoria fiscal para um cenário específico
    """
    try:
        data = request.get_json()

        # Obter parâmetros
        tipo_tributo = data.get('tipo_tributo')  # icms, icms_st, ipi, pis, cofins, difal
        forcar_recalculo = data.get('forcar_recalculo', False)

        # Validar parâmetros
        if not tipo_tributo:
            return jsonify({
                'success': False,
                'message': 'Tipo de tributo não informado'
            }), 400

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 404

        # Buscar o cenário
        CenarioModel = {
            'icms': db.session.get(db.Model, 'CenarioICMS'),
            'icms_st': db.session.get(db.Model, 'CenarioICMSST'),
            'ipi': db.session.get(db.Model, 'CenarioIPI'),
            'pis': db.session.get(db.Model, 'CenarioPIS'),
            'cofins': db.session.get(db.Model, 'CenarioCOFINS'),
            'difal': db.session.get(db.Model, 'CenarioDIFAL')
        }[tipo_tributo]

        cenario = db.session.get(CenarioModel, cenario_id)

        if not cenario:
            return jsonify({
                'success': False,
                'message': f'Cenário de {tipo_tributo.upper()} não encontrado'
            }), 404

        # Inicializar serviço de auditoria
        auditoria_service = AuditoriaService(cenario.empresa_id, usuario_id)

        # Executar auditoria para os tributos relacionados ao cenário
        resultado = auditoria_service.executar_auditoria(
            produto_ids=[cenario.produto_id],
            cliente_ids=[cenario.cliente_id],
            forcar_recalculo=forcar_recalculo,
            tipo_tributo=tipo_tributo
        )

        return jsonify(resultado)

    except Exception as e:
        logger.error(f"Erro ao executar auditoria por cenário: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao executar auditoria por cenário: {str(e)}'
        }), 500


@auditoria_bp.route('/api/auditoria/marcar-vista/<int:resultado_id>', methods=['POST'])
@jwt_required()
def marcar_inconsistencia_vista(resultado_id):
    """
    Marca uma inconsistência como vista pelo analista
    """
    try:
        data = request.get_json()
        observacoes = data.get('observacoes', '')

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar o resultado de auditoria
        resultado = db.session.get(AuditoriaResultado, resultado_id)

        if not resultado:
            return jsonify({
                'success': False,
                'message': 'Resultado de auditoria não encontrado'
            }), 404

        # Verificar se o resultado é inconsistente
        if resultado.status != 'inconsistente':
            return jsonify({
                'success': False,
                'message': 'Apenas inconsistências podem ser marcadas como vistas'
            }), 400

        # Marcar como vista
        resultado.analista_visualizou = True
        resultado.observacoes_analista = observacoes
        resultado.data_visualizacao = datetime.now()
        resultado.usuario_analista_id = usuario_id

        db.session.commit()

        logger.info(f"Inconsistência {resultado_id} marcada como vista pelo usuário {usuario_id}")

        return jsonify({
            'success': True,
            'message': 'Inconsistência marcada como vista com sucesso',
            'resultado': resultado.to_dict()
        }), 200

    except Exception as e:
        logger.error(f"Erro ao marcar inconsistência como vista: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'Erro ao marcar inconsistência como vista: {str(e)}'
        }), 500
