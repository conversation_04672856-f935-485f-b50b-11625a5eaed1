from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, Usuario, Produto, Empresa, NotaFiscalItem, CenarioICMS
from sqlalchemy import func
from datetime import datetime

produto_bp = Blueprint('produto_bp', __name__)

@produto_bp.route('/api/produtos', methods=['GET'])
@jwt_required()
def listar_produtos():
    """
    Lista os produtos com base nas permissões do usuário com suporte a paginação
    """
    try:
        # Obter parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)
        status = request.args.get('status')

        # Parâmetros de paginação
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 100, type=int)  # Default 100 itens por página

        # Limitar o número máximo de itens por página para evitar sobrecarga
        if per_page > 500:
            per_page = 500

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Construir a query base
        query = Produto.query

        # Filtrar por empresa se especificado
        if empresa_id:
            query = query.filter_by(empresa_id=empresa_id)

        # Filtrar por status se especificado
        if status:
            query = query.filter_by(status=status)

        # Aplicar filtros com base no tipo de usuário
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores veem todos os produtos
            pass
        elif usuario.tipo_usuario == 'escritorio':
            # Usuários do tipo escritório veem produtos do seu escritório
            query = query.filter_by(escritorio_id=usuario.escritorio_id)
        else:
            # Usuários comuns veem apenas produtos das empresas permitidas
            empresas_permitidas = usuario.empresas_permitidas or []
            query = query.filter(Produto.empresa_id.in_(empresas_permitidas))

        # Contar total de registros para paginação
        total_count = query.count()

        # Aplicar paginação
        produtos_paginados = query.order_by(Produto.id.desc()).paginate(page=page, per_page=per_page, error_out=False)

        # Converter para dicionários
        produtos_dict = []
        for produto in produtos_paginados.items:
            produto_dict = produto.to_dict()

            # Buscar o NCM e CFOP mais recentes da tabela nota_fiscal_item
            nota_fiscal_item = NotaFiscalItem.query.filter_by(
                produto_id=produto.id
            ).order_by(NotaFiscalItem.data_emissao.desc()).first()

            if nota_fiscal_item:
                produto_dict['ncm'] = nota_fiscal_item.ncm
                produto_dict['cfop'] = nota_fiscal_item.cfop
            else:
                # Se não encontrar na tabela nota_fiscal_item, buscar na tabela cenario_icms
                cenario = CenarioICMS.query.filter_by(
                    produto_id=produto.id
                ).order_by(CenarioICMS.data_atualizacao.desc()).first()

                if cenario:
                    produto_dict['ncm'] = cenario.ncm
                    produto_dict['cfop'] = cenario.cfop
                else:
                    produto_dict['ncm'] = '-'
                    produto_dict['cfop'] = '-'

            produtos_dict.append(produto_dict)

        # Retornar dados com informações de paginação
        return jsonify({
            "produtos": produtos_dict,
            "pagination": {
                "total": total_count,
                "per_page": per_page,
                "current_page": page,
                "last_page": produtos_paginados.pages,
                "from": (page - 1) * per_page + 1 if produtos_paginados.items else 0,
                "to": (page - 1) * per_page + len(produtos_paginados.items) if produtos_paginados.items else 0,
                "has_prev": produtos_paginados.has_prev,
                "has_next": produtos_paginados.has_next,
                "prev_page": produtos_paginados.prev_num if produtos_paginados.has_prev else None,
                "next_page": produtos_paginados.next_num if produtos_paginados.has_next else None
            }
        }), 200

    except Exception as e:
        print(f"Erro ao listar produtos: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@produto_bp.route('/api/produtos/<int:produto_id>', methods=['GET'])
@jwt_required()
def obter_produto(produto_id):
    """
    Obtém os detalhes de um produto específico
    """
    try:
        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar o produto
        produto = db.session.get(Produto, produto_id)

        if not produto:
            return jsonify({"message": "Produto não encontrado"}), 404

        # Verificar permissões para visualizar o produto
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores podem ver qualquer produto
            pass
        elif usuario.tipo_usuario == 'escritorio' and produto.escritorio_id == usuario.escritorio_id:
            # Usuários do tipo escritório só podem ver produtos do seu próprio escritório
            pass
        elif usuario.empresas_permitidas and produto.empresa_id in usuario.empresas_permitidas:
            # Usuários comuns só podem ver produtos das empresas permitidas
            pass
        else:
            return jsonify({"message": "Você não tem permissão para visualizar este produto"}), 403

        produto_dict = produto.to_dict()

        # Buscar o NCM e CFOP mais recentes da tabela nota_fiscal_item
        nota_fiscal_item = NotaFiscalItem.query.filter_by(
            produto_id=produto.id
        ).order_by(NotaFiscalItem.data_emissao.desc()).first()

        if nota_fiscal_item:
            produto_dict['ncm'] = nota_fiscal_item.ncm
            produto_dict['cfop'] = nota_fiscal_item.cfop
        else:
            # Se não encontrar na tabela nota_fiscal_item, buscar na tabela cenario_icms
            cenario = CenarioICMS.query.filter_by(
                produto_id=produto.id
            ).order_by(CenarioICMS.data_atualizacao.desc()).first()

            if cenario:
                produto_dict['ncm'] = cenario.ncm
                produto_dict['cfop'] = cenario.cfop
            else:
                produto_dict['ncm'] = '-'
                produto_dict['cfop'] = '-'

        return jsonify({
            "produto": produto_dict
        }), 200

    except Exception as e:
        print(f"Erro ao obter produto: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@produto_bp.route('/api/produtos/<int:produto_id>', methods=['PUT'])
@jwt_required()
def atualizar_produto(produto_id):
    """
    Atualiza os dados de um produto
    """
    try:
        data = request.get_json()

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar o produto
        produto = db.session.get(Produto, produto_id)

        if not produto:
            return jsonify({"message": "Produto não encontrado"}), 404

        # Verificar permissões para atualizar o produto
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores podem atualizar qualquer produto
            pass
        elif usuario.tipo_usuario == 'escritorio' and produto.escritorio_id == usuario.escritorio_id:
            # Usuários do tipo escritório só podem atualizar produtos do seu próprio escritório
            pass
        elif usuario.empresas_permitidas and produto.empresa_id in usuario.empresas_permitidas:
            # Usuários comuns só podem atualizar produtos das empresas permitidas
            pass
        else:
            return jsonify({"message": "Você não tem permissão para atualizar este produto"}), 403

        # Atualizar os campos do produto
        if 'descricao' in data:
            produto.descricao = data['descricao']
        # Campos ncm e cfop removidos - agora estão apenas nas tabelas nota_fiscal_item e cenarios
        if 'unidade_comercial' in data:
            produto.unidade_comercial = data['unidade_comercial']
        if 'unidade_tributavel' in data:
            produto.unidade_tributavel = data['unidade_tributavel']
        if 'codigo_ean' in data:
            produto.codigo_ean = data['codigo_ean']
        if 'codigo_ean_tributavel' in data:
            produto.codigo_ean_tributavel = data['codigo_ean_tributavel']
        if 'unidade_tributaria' in data:
            produto.unidade_tributaria = data['unidade_tributaria']
        if 'tipo_sped' in data:
            produto.tipo_sped = data['tipo_sped']
        if 'cest' in data:
            produto.cest = data['cest']
        if 'status' in data:
            produto.status = data['status']

        db.session.commit()

        produto_dict = produto.to_dict()

        # Buscar o NCM e CFOP mais recentes da tabela nota_fiscal_item
        nota_fiscal_item = NotaFiscalItem.query.filter_by(
            produto_id=produto.id
        ).order_by(NotaFiscalItem.data_emissao.desc()).first()

        if nota_fiscal_item:
            produto_dict['ncm'] = nota_fiscal_item.ncm
            produto_dict['cfop'] = nota_fiscal_item.cfop
        else:
            # Se não encontrar na tabela nota_fiscal_item, buscar na tabela cenario_icms
            cenario = CenarioICMS.query.filter_by(
                produto_id=produto.id
            ).order_by(CenarioICMS.data_atualizacao.desc()).first()

            if cenario:
                produto_dict['ncm'] = cenario.ncm
                produto_dict['cfop'] = cenario.cfop
            else:
                produto_dict['ncm'] = '-'
                produto_dict['cfop'] = '-'

        return jsonify({
            "message": "Produto atualizado com sucesso",
            "produto": produto_dict
        }), 200

    except Exception as e:
        db.session.rollback()
        print(f"Erro ao atualizar produto: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@produto_bp.route('/api/produtos', methods=['POST'])
@jwt_required()
def criar_produto():
    """
    Cria um novo produto
    """
    try:
        data = request.get_json()

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Verificar se os campos obrigatórios estão presentes
        if not data.get('codigo') or not data.get('descricao') or not data.get('empresa_id'):
            return jsonify({"message": "Código, Descrição e ID da Empresa são obrigatórios"}), 400

        # Verificar se o usuário tem permissão para a empresa
        empresa_id = data.get('empresa_id')
        empresa = db.session.get(Empresa, empresa_id)

        if not empresa:
            return jsonify({"message": "Empresa não encontrada"}), 404

        # Verificar permissões para criar produto na empresa
        if not (usuario.is_admin or usuario.tipo_usuario == 'admin' or
                (usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id) or
                (usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas)):
            return jsonify({"message": "Você não tem permissão para criar produtos nesta empresa"}), 403

        # Verificar se já existe um produto com o mesmo código na empresa
        produto_existente = Produto.query.filter_by(
            empresa_id=empresa_id,
            codigo=data.get('codigo')
        ).first()

        if produto_existente:
            return jsonify({"message": "Já existe um produto com este código nesta empresa"}), 400

        # Criar o novo produto
        produto = Produto(
            empresa_id=empresa_id,
            escritorio_id=empresa.escritorio_id,
            codigo=data.get('codigo'),
            descricao=data.get('descricao'),
            # Campos ncm e cfop removidos - agora estão apenas nas tabelas nota_fiscal_item e cenarios
            unidade_comercial=data.get('unidade_comercial'),
            unidade_tributavel=data.get('unidade_tributavel'),
            codigo_ean=data.get('codigo_ean'),
            codigo_ean_tributavel=data.get('codigo_ean_tributavel'),
            unidade_tributaria=data.get('unidade_tributaria'),
            tipo_sped=data.get('tipo_sped'),
            cest=data.get('cest'),
            data_cadastro=datetime.now(),
            status=data.get('status', 'novo')
        )

        db.session.add(produto)
        db.session.commit()

        produto_dict = produto.to_dict()

        # Para produtos novos, não há dados de NCM e CFOP ainda
        produto_dict['ncm'] = '-'
        produto_dict['cfop'] = '-'

        return jsonify({
            "message": "Produto criado com sucesso",
            "produto": produto_dict
        }), 201

    except Exception as e:
        db.session.rollback()
        print(f"Erro ao criar produto: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@produto_bp.route('/api/produtos/<int:produto_id>', methods=['DELETE'])
@jwt_required()
def excluir_produto(produto_id):
    """
    Exclui um produto
    """
    try:
        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar o produto
        produto = db.session.get(Produto, produto_id)

        if not produto:
            return jsonify({"message": "Produto não encontrado"}), 404

        # Verificar permissões para excluir o produto
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores podem excluir qualquer produto
            pass
        elif usuario.tipo_usuario == 'escritorio' and produto.escritorio_id == usuario.escritorio_id:
            # Usuários do tipo escritório só podem excluir produtos do seu próprio escritório
            pass
        elif usuario.empresas_permitidas and produto.empresa_id in usuario.empresas_permitidas:
            # Usuários comuns só podem excluir produtos das empresas permitidas
            pass
        else:
            return jsonify({"message": "Você não tem permissão para excluir este produto"}), 403

        # Excluir o produto
        db.session.delete(produto)
        db.session.commit()

        return jsonify({
            "message": "Produto excluído com sucesso"
        }), 200

    except Exception as e:
        db.session.rollback()
        print(f"Erro ao excluir produto: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500