# Documentação do Sistema de Auditoria Fiscal

## Visão Geral

O Sistema de Auditoria Fiscal é uma aplicação web desenvolvida para auxiliar empresas e escritórios de contabilidade na gestão e auditoria de tributos. O sistema permite o cadastro de empresas, clientes e produtos, importação de XMLs de notas fiscais, e a criação e gerenciamento de cenários tributários para diferentes tipos de impostos (ICMS, ICMS-ST, IPI, PIS, COFINS, DIFAL).

## Arquitetura do Sistema

O sistema segue uma arquitetura cliente-servidor:

- **Frontend**: Desenvolvido em HTML, CSS e JavaScript, utilizando Bootstrap para o layout e DataTables para exibição de dados tabulares.
- **Backend**: Desenvolvido em Python com Flask, utilizando SQLAlchemy como ORM.
- **Banco de Dados**: PostgreSQL, com tabelas para empresas, clientes, produtos, tributos e cenários.

## Estrutura do Banco de Dados

### Tabelas Principais

1. **empresa**: Armazena informações das empresas cadastradas no sistema.
2. **cliente**: Armazena informações dos clientes das empresas.
3. **produto**: Armazena informações dos produtos comercializados.
4. **tributo**: Armazena informações dos tributos importados de XMLs.
5. **tributo_historico**: Armazena o histórico de alterações nos tributos.
6. **cenario_icms**, **cenario_icms_st**, **cenario_ipi**, **cenario_pis**, **cenario_cofins**, **cenario_difal**: Armazenam os cenários tributários para cada tipo de imposto.

### Status dos Cenários

Os cenários podem ter os seguintes status:

- **novo**: Cenário recém-importado, ainda não validado.
- **producao**: Cenário validado e em uso.
- **inconsistente**: Cenário que apresenta inconsistências em relação ao cenário em produção.

## Fluxo de Importação de XML

1. O usuário faz upload de um arquivo XML de nota fiscal.
2. O sistema extrai os dados do XML, incluindo informações do emitente, destinatário, produtos e tributos.
3. O sistema verifica se o cliente (emitente/destinatário) já existe no banco de dados:
   - Se não existir, cria um novo registro de cliente com os dados do XML.
   - Se existir, utiliza o registro existente.
4. O sistema verifica se o produto já existe no banco de dados:
   - Se não existir, cria um novo registro de produto com os dados do XML.
   - Se existir, utiliza o registro existente.
5. O sistema cria registros de tributos para cada item da nota fiscal, com status 'novo'.
6. O sistema cria cenários tributários para cada tipo de imposto (ICMS, ICMS-ST, IPI, PIS, COFINS, DIFAL) com status 'novo'.

## Fluxo de Validação de Cenários

1. O usuário acessa a página de detalhes de cenários para um tipo específico de tributo.
2. O sistema exibe os cenários em três abas: Novos, Produção e Inconsistentes.
3. O usuário pode:
   - Enviar um cenário novo para produção, definindo sua data de início de vigência.
   - Editar um cenário existente.
   - Ativar ou desativar um cenário em produção.
   - Definir a vigência de um cenário em produção.
4. Quando um cenário é enviado para produção:
   - O sistema verifica se já existe um cenário em produção para o mesmo cliente/produto/tributo.
   - Se existir, o cenário anterior é desativado.
   - O sistema recalcula os valores dos tributos com base no novo cenário. (Isso não pode acontecer, produtos que já tiveram seu status atualizado para "conforme", não podem ter seus dados alterados.)
   - O sistema verifica outros cenários novos ou inconsistentes para o mesmo cliente/produto/tributo e atualiza seu status conforme necessário.

## Cálculo de Tributos

O sistema realiza cálculos específicos para cada tipo de tributo:

### ICMS

- Aplica redução da base de cálculo, se houver.
- Calcula o valor do ICMS com base na alíquota definida.
- Aplica diferimento, se houver.

### ICMS-ST

- Aplica redução da base de cálculo, se houver.
- Calcula o valor do ICMS próprio.
- Aplica MVA (Margem de Valor Agregado) para calcular a base de cálculo do ICMS-ST.
- Calcula o valor do ICMS-ST com base na alíquota definida.

### IPI

- Calcula o valor do IPI com base na alíquota definida.

### PIS/COFINS

- Aplica redução da base de cálculo, se houver.
- Calcula o valor do PIS/COFINS com base na alíquota definida.

### DIFAL

- Calcula a diferença entre as alíquotas de ICMS do estado de origem e destino.
- Aplica o percentual de partilha, se aplicável.
- Calcula o FCP (Fundo de Combate à Pobreza), se aplicável.

## Interfaces do Sistema

### Sidebar

- Home
- Auditoria (entrada/saída)
  - ICMS
  - ICMS-ST
  - DIFAL
  - IPI
  - PIS
  - COFINS
- Cenários (entrada/saída)
  - ICMS
  - ICMS-ST
  - DIFAL
  - IPI
  - PIS
  - COFINS
- Clientes
- Produtos
- Importação

### Header

- Filtro de empresa
- Filtro de ano
- Filtro de mês
- Perfil do usuário (com acesso à página de escritórios para usuários admin)

### Páginas Principais

1. **Dashboard**: Visão geral do sistema.
2. **Auditoria**: Exibe os tributos importados, organizados por tipo.
3. **Cenários**: Exibe os cenários tributários, organizados por tipo e status.
4. **Clientes**: Gerenciamento de clientes.
5. **Produtos**: Gerenciamento de produtos.
6. **Importação**: Upload e processamento de arquivos XML.
7. **Escritórios**: Gerenciamento de escritórios de contabilidade (apenas para admin).

## Funções do Banco de Dados

O sistema utiliza diversas funções no banco de dados para:

1. **Cálculo de tributos**: Funções específicas para calcular cada tipo de tributo.
2. **Atualização de status**: Funções para atualizar o status dos cenários.
3. **Verificação de consistência**: Funções para verificar a consistência entre cenários.
4. **Histórico de tributos**: Funções para manter o histórico de alterações nos tributos.

## Considerações Importantes

1. O sistema não utiliza mais o status 'conforme', mantendo apenas os status 'novo', 'producao' e 'inconsistente'.
2. A importação de XML verifica automaticamente o CNPJ do emitente/destinatário com as empresas cadastradas.
3. O sistema categoriza os produtos como Novos/Produção/Inconsistentes com base na comparação com dados existentes.
4. O sistema captura tags adicionais do XML para cada tipo de tributo (ICMS-ST, ICMS, DIFAL, etc.).
5. Quando um produto tem seu status atualizado para 'producao', o sistema recalcula automaticamente o status de outros tributos do mesmo produto.

## API Endpoints

### Cenários

- `GET /api/cenarios/<tipo_tributo>`: Lista todos os cenários para um tipo de tributo.
- `GET /api/cenarios/<tipo_tributo>/<id>`: Obtém detalhes de um cenário específico.
- `POST /api/cenarios/<tipo_tributo>`: Cria um novo cenário.
- `PUT /api/cenarios/<tipo_tributo>/<id>`: Atualiza um cenário existente.
- `DELETE /api/cenarios/<tipo_tributo>/<id>`: Remove um cenário.
- `PUT /api/cenarios/<tipo_tributo>/<id>/status`: Atualiza o status de um cenário.
- `PUT /api/cenarios/<tipo_tributo>/<id>/ativar`: Ativa um cenário.
- `PUT /api/cenarios/<tipo_tributo>/<id>/desativar`: Desativa um cenário.
- `PUT /api/cenarios/<tipo_tributo>/<id>/vigencia`: Define a vigência de um cenário.
- `GET /api/cenarios/<tipo_tributo>/count`: Conta cenários por status.

### Clientes

- `GET /api/clientes`: Lista todos os clientes.
- `GET /api/clientes/<id>`: Obtém detalhes de um cliente específico.
- `POST /api/clientes`: Cria um novo cliente.
- `PUT /api/clientes/<id>`: Atualiza um cliente existente.
- `DELETE /api/clientes/<id>`: Remove um cliente.

### Produtos

- `GET /api/produtos`: Lista todos os produtos.
- `GET /api/produtos/<id>`: Obtém detalhes de um produto específico.
- `POST /api/produtos`: Cria um novo produto.
- `PUT /api/produtos/<id>`: Atualiza um produto existente.
- `DELETE /api/produtos/<id>`: Remove um produto.

### Importação

- `POST /api/importacao/xml`: Importa um arquivo XML.

## Serviços

### CenarioService

Responsável por gerenciar os cenários tributários:

- Criação, atualização e remoção de cenários.
- Atualização de status de cenários.
- Ativação e desativação de cenários.
- Definição de vigência de cenários.
- Cálculo de tributos com base nos cenários.
- Verificação de consistência entre cenários.

### ImportacaoService

Responsável pela importação de arquivos XML:

- Extração de dados do XML.
- Criação de registros de clientes, produtos e tributos.
- Criação de cenários tributários com base nos dados importados.

### ClienteService

Responsável pelo gerenciamento de clientes:

- Criação, atualização e remoção de clientes.
- Busca de clientes por CNPJ/CPF.
- Atualização de status de clientes.

### ProdutoService

Responsável pelo gerenciamento de produtos:

- Criação, atualização e remoção de produtos.
- Busca de produtos por código, descrição ou NCM.
- Atualização de status de produtos.

## Frontend

### Componentes Principais

1. **Sidebar**: Navegação principal do sistema.
2. **Header**: Filtros globais e acesso ao perfil do usuário.
3. **Cards**: Exibição de informações resumidas sobre tributos.
4. **Tabelas**: Exibição de dados detalhados com recursos de filtragem, ordenação e paginação.
5. **Modais**: Formulários para criação e edição de registros.
6. **Tabs**: Organização de conteúdo por status (Novos, Produção, Inconsistentes).

### JavaScript

O frontend utiliza JavaScript para:

1. **Inicialização de componentes**: Configuração de DataTables, seletores de colunas, etc.
2. **Comunicação com a API**: Requisições AJAX para obter e enviar dados.
3. **Manipulação do DOM**: Atualização dinâmica da interface.
4. **Validação de formulários**: Verificação de campos obrigatórios e formatos.
5. **Gerenciamento de eventos**: Tratamento de cliques, submissões de formulários, etc.

### Fluxo de Interação

1. O usuário navega pelo sistema através da sidebar.
2. Ao acessar uma página, o sistema carrega os dados relevantes via API.
3. O usuário pode filtrar, ordenar e paginar os dados exibidos.
4. O usuário pode criar, editar ou excluir registros através de modais.
5. Ao realizar uma ação, o sistema atualiza a interface e exibe mensagens de feedback.

## Correções Realizadas

1. **Remoção do status 'conforme'**:

   - Removido o status 'conforme' das tabelas de cenários.
   - Substituída a função `atualizar_status_cenarios_conforme` por `verificar_cenarios_compativeis`.
   - Removida a aba 'Conformes' da interface.
   - Atualizados os botões de ação para não incluir opções relacionadas ao status 'conforme'.

2. **Correção de erros no SQL**:

   - Corrigido o problema de declaração duplicada de variáveis no bloco SQL.
   - Adicionado um bloco BEGIN/END para encapsular as declarações de variáveis.

3. **Atualização da documentação**:
   - Criada documentação completa do sistema, incluindo arquitetura, fluxos, API e serviços.

## Próximos Passos Recomendados

1. **Testes**:

   - Realizar testes de integração para verificar o funcionamento correto do sistema após as alterações.
   - Testar especificamente o fluxo de atualização de status de cenários.

2. **Monitoramento**:

   - Implementar logs detalhados para rastrear o fluxo de execução do sistema.
   - Monitorar o desempenho do banco de dados, especialmente as funções de cálculo de tributos.

3. **Melhorias Futuras**:
   - Implementar cache para melhorar o desempenho do sistema.
   - Adicionar recursos de exportação de dados em diferentes formatos (CSV, Excel, PDF).
   - Implementar dashboards com gráficos e indicadores para facilitar a análise de dados.
