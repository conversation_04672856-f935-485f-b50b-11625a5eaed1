from .escritorio import db
from sqlalchemy.sql import func

class Empresa(db.Model):
    __tablename__ = 'empresa'
    id = db.Column(db.Integer, primary_key=True)
    escritorio_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('escritorio.id'))
    razao_social = db.Column(db.String(255), nullable=False)
    cnpj = db.Column(db.String(18), nullable=False, unique=True)
    inscricao_estadual = db.Column(db.String(20))
    data_cadastro = db.Column(db.DateTime, server_default=func.now())

    nome_fantasia = db.Column(db.String(255))
    email = db.Column(db.String(255))
    responsavel = db.Column(db.String(255))
    cep = db.Column(db.String(10))
    logradouro = db.Column(db.String(255))
    numero = db.Column(db.String(20))
    complemento = db.Column(db.String(255))
    bairro = db.Column(db.String(100))
    cidade = db.Column(db.String(100))
    estado = db.Column(db.String(2))
    cnae = db.Column(db.String(20))
    tributacao = db.Column(db.String(50))
    atividade = db.Column(db.String(50))
    pis_cofins = db.Column(db.String(20))
    observacoes = db.Column(db.Text)

    def __repr__(self):
        return f"<Empresa {self.razao_social}>"

    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'escritorio_id': self.escritorio_id,
            'razao_social': self.razao_social,
            'cnpj': self.cnpj,
            'inscricao_estadual': self.inscricao_estadual,
            'data_cadastro': self.data_cadastro.isoformat() if self.data_cadastro else None,
            'nome_fantasia': self.nome_fantasia,
            'email': self.email,
            'responsavel': self.responsavel,
            'cep': self.cep,
            'logradouro': self.logradouro,
            'numero': self.numero,
            'complemento': self.complemento,
            'bairro': self.bairro,
            'cidade': self.cidade,
            'estado': self.estado,
            'cnae': self.cnae,
            'tributacao': self.tributacao,
            'atividade': self.atividade,
            'pis_cofins': self.pis_cofins,
            'observacoes': self.observacoes
        }
