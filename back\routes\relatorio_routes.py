from flask import Blueprint, request, jsonify, Response
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, Usuario, Empresa, Escritorio, AuditoriaResultado, AuditoriaSumario, NotaFiscalItem, Tributo, Cliente, <PERSON>duto
from sqlalchemy import and_, or_
from io import BytesIO
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

relatorio_bp = Blueprint('relatorio', __name__)

@relatorio_bp.route('/api/relatorios/tributo/<tipo_tributo>', methods=['GET'])
@jwt_required()
def gerar_relatorio_tributo(tipo_tributo):
    """
    Gera relatório PDF específico para um tipo de tributo
    """
    try:
        from reportlab.lib.pagesizes import A4, landscape
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch, mm
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
        import os

        # Obter parâmetros
        empresa_id = request.args.get('empresa_id', type=int)
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        status = request.args.get('status', 'inconsistente')  # 'todos', 'conforme', 'inconsistente'

        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        if not empresa_id:
            return jsonify({"message": "ID da empresa é obrigatório"}), 400

        # Verificar permissões
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa:
            return jsonify({"message": "Empresa não encontrada"}), 404

        # Verificar se o usuário tem permissão para visualizar a empresa
        if not (usuario.is_admin or usuario.tipo_usuario == 'admin' or
                (usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id) or
                (usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas)):
            return jsonify({"message": "Você não tem permissão para gerar relatórios desta empresa"}), 403

        # Buscar dados do escritório
        escritorio = db.session.get(Escritorio, empresa.escritorio_id)

        # Buscar resultados de auditoria
        logger.info(f"Buscando resultados para empresa_id={empresa_id}, tipo_tributo={tipo_tributo}")

        query = AuditoriaResultado.query.filter(
            AuditoriaResultado.empresa_id == empresa_id,
            AuditoriaResultado.tipo_tributo == tipo_tributo
        )

        # Filtrar por período se especificado
        if year and month:
            logger.info(f"Filtrando por período: {year}/{month}")
            query = query.join(Tributo, AuditoriaResultado.tributo_id == Tributo.id).filter(
                db.extract('year', Tributo.data_emissao) == year,
                db.extract('month', Tributo.data_emissao) == month
            )

        # Filtrar por status se especificado
        if status != 'todos':
            logger.info(f"Filtrando por status: {status}")
            query = query.filter(AuditoriaResultado.status == status)

        resultados = query.all()
        logger.info(f"Encontrados {len(resultados)} resultados")

        if not resultados:
            logger.warning("Nenhum resultado encontrado para os filtros especificados")
            return jsonify({"message": "Nenhum resultado encontrado para os filtros especificados"}), 404

        # Criar PDF
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=landscape(A4),
                              leftMargin=20*mm, rightMargin=20*mm,
                              topMargin=20*mm, bottomMargin=20*mm)
        styles = getSampleStyleSheet()
        story = []

        # Estilo personalizado para título
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )

        # Estilo para subtítulo
        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=15,
            alignment=TA_CENTER
        )

        # Cabeçalho com logo e informações do escritório
        header_data = []

        # Verificar se existe logo
        logo_path = None
        if escritorio and escritorio.logo_path:
            logo_full_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                        'front', escritorio.logo_path.lstrip('/'))
            if os.path.exists(logo_full_path):
                logo_path = logo_full_path

        if logo_path:
            try:
                logo = Image(logo_path, width=60*mm, height=40*mm)
                header_data.append([logo, f"<b>{escritorio.nome}</b><br/>Responsável: {escritorio.responsavel or 'N/A'}<br/>CNPJ: {escritorio.cnpj}"])
            except:
                header_data.append(["", f"<b>{escritorio.nome}</b><br/>Responsável: {escritorio.responsavel or 'N/A'}<br/>CNPJ: {escritorio.cnpj}"])
        else:
            header_data.append(["", f"<b>{escritorio.nome}</b><br/>Responsável: {escritorio.responsavel or 'N/A'}<br/>CNPJ: {escritorio.cnpj}"])

        header_table = Table(header_data, colWidths=[60*mm, 150*mm])
        header_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTSIZE', (1, 0), (1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 20),
        ]))
        story.append(header_table)

        # Título do relatório
        titulo = f"RELATÓRIO DE AUDITORIA FISCAL - {tipo_tributo.upper().replace('_', '-')}"
        story.append(Paragraph(titulo, title_style))

        # Informações da empresa e período
        info_text = f"<b>Empresa:</b> {empresa.razao_social}<br/><b>CNPJ:</b> {empresa.cnpj}"
        if year and month:
            info_text += f"<br/><b>Período:</b> {month:02d}/{year}"
        if status != 'todos':
            info_text += f"<br/><b>Status:</b> {status.title()}"

        story.append(Paragraph(info_text, subtitle_style))
        story.append(Spacer(1, 20))

        # Continuar na próxima parte...
        return _continuar_relatorio_tributo(story, resultados, doc, buffer, tipo_tributo, empresa_id)

    except ImportError:
        return jsonify({
            'success': False,
            'message': 'Biblioteca reportlab não instalada. Execute: pip install reportlab'
        }), 500
    except Exception as e:
        logger.error(f"Erro ao gerar relatório: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao gerar relatório: {str(e)}'
        }), 500

def _continuar_relatorio_tributo(story, resultados, doc, buffer, tipo_tributo, empresa_id):
    """
    Continua a geração do relatório de tributo
    """
    from reportlab.platypus import Table, TableStyle
    from reportlab.lib import colors
    from reportlab.lib.units import mm

    # Cabeçalho da tabela
    headers = ['Nota Fiscal', 'Cliente', 'Produto', 'NCM', 'CFOP', 'CST',
               'Base Cálculo Nota', 'Alíquota Nota', 'Valor Nota',
               'Base Cálculo Cenário', 'Alíquota Cenário', 'Valor Cenário', 'Diferença']

    table_data = [headers]

    # Dados da tabela
    for resultado in resultados:
        # Buscar dados relacionados
        tributo = db.session.get(Tributo, resultado.tributo_id)
        nota_item = db.session.get(NotaFiscalItem, resultado.nota_fiscal_item_id)
        cliente = db.session.get(Cliente, tributo.cliente_id) if tributo else None
        produto = db.session.get(Produto, tributo.produto_id) if tributo else None

        # Calcular diferença
        diferenca = 0
        if resultado.valor_calculado and resultado.valor_nota:
            diferenca = float(resultado.valor_calculado) - float(resultado.valor_nota)

        row = [
            tributo.numero_nf if tributo else 'N/A',
            cliente.razao_social[:30] + '...' if cliente and len(cliente.razao_social) > 30 else (cliente.razao_social if cliente else 'N/A'),
            produto.descricao[:25] + '...' if produto and len(produto.descricao) > 25 else (produto.descricao if produto else 'N/A'),
            nota_item.ncm if nota_item else 'N/A',
            nota_item.cfop if nota_item else 'N/A',
            getattr(resultado, f'cst_nota', 'N/A'),
            f"R$ {float(resultado.base_calculo_nota):,.2f}" if resultado.base_calculo_nota else 'R$ 0,00',
            f"{float(resultado.aliquota_nota):,.2f}%" if resultado.aliquota_nota else '0,00%',
            f"R$ {float(resultado.valor_nota):,.2f}" if resultado.valor_nota else 'R$ 0,00',
            f"R$ {float(resultado.base_calculo_calculada):,.2f}" if resultado.base_calculo_calculada else 'R$ 0,00',
            f"{float(resultado.aliquota_cenario):,.2f}%" if resultado.aliquota_cenario else '0,00%',
            f"R$ {float(resultado.valor_calculado):,.2f}" if resultado.valor_calculado else 'R$ 0,00',
            f"R$ {diferenca:,.2f}"
        ]
        table_data.append(row)

    # Criar tabela
    table = Table(table_data, repeatRows=1)
    table.setStyle(TableStyle([
        # Cabeçalho
        ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 8),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),

        # Dados
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 7),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),

        # Zebra striping
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
    ]))

    story.append(table)

    # Gerar PDF
    doc.build(story)
    buffer.seek(0)

    return Response(
        buffer.getvalue(),
        mimetype='application/pdf',
        headers={
            'Content-Disposition': f'attachment; filename=relatorio_{tipo_tributo}_{empresa_id}.pdf'
        }
    )

@relatorio_bp.route('/api/relatorios/geral', methods=['GET'])
@jwt_required()
def gerar_relatorio_geral():
    """
    Gera relatório PDF geral com todos os tipos de tributos
    """
    try:
        from reportlab.lib.pagesizes import A4, landscape
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image, PageBreak
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch, mm
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
        import os

        # Obter parâmetros
        empresa_id = request.args.get('empresa_id', type=int)
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        status = request.args.get('status', 'inconsistente')  # 'todos', 'conforme', 'inconsistente'

        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        if not empresa_id:
            return jsonify({"message": "ID da empresa é obrigatório"}), 400

        # Verificar permissões
        empresa = db.session.get(Empresa, empresa_id)
        if not empresa:
            return jsonify({"message": "Empresa não encontrada"}), 404

        # Verificar se o usuário tem permissão para visualizar a empresa
        if not (usuario.is_admin or usuario.tipo_usuario == 'admin' or
                (usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id) or
                (usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas)):
            return jsonify({"message": "Você não tem permissão para gerar relatórios desta empresa"}), 403

        # Buscar dados do escritório
        escritorio = db.session.get(Escritorio, empresa.escritorio_id)

        # Tipos de tributos
        tipos_tributos = ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal']

        # Buscar resultados de auditoria para todos os tributos
        query = AuditoriaResultado.query.filter(
            AuditoriaResultado.empresa_id == empresa_id,
            AuditoriaResultado.tipo_tributo.in_(tipos_tributos)
        )

        # Filtrar por período se especificado
        if year and month:
            query = query.join(Tributo, AuditoriaResultado.tributo_id == Tributo.id).filter(
                db.extract('year', Tributo.data_emissao) == year,
                db.extract('month', Tributo.data_emissao) == month
            )

        # Filtrar por status se especificado
        if status != 'todos':
            query = query.filter(AuditoriaResultado.status == status)

        resultados = query.all()

        if not resultados:
            return jsonify({"message": "Nenhum resultado encontrado para os filtros especificados"}), 404

        # Agrupar resultados por tipo de tributo
        resultados_por_tributo = {}
        for resultado in resultados:
            tipo = resultado.tipo_tributo
            if tipo not in resultados_por_tributo:
                resultados_por_tributo[tipo] = []
            resultados_por_tributo[tipo].append(resultado)

        # Criar PDF
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=landscape(A4),
                              leftMargin=20*mm, rightMargin=20*mm,
                              topMargin=20*mm, bottomMargin=20*mm)

        story = _criar_relatorio_geral_content(escritorio, empresa, resultados_por_tributo, year, month, status)

        # Gerar PDF
        doc.build(story)
        buffer.seek(0)

        return Response(
            buffer.getvalue(),
            mimetype='application/pdf',
            headers={
                'Content-Disposition': f'attachment; filename=relatorio_geral_{empresa_id}.pdf'
            }
        )

    except ImportError:
        return jsonify({
            'success': False,
            'message': 'Biblioteca reportlab não instalada. Execute: pip install reportlab'
        }), 500
    except Exception as e:
        logger.error(f"Erro ao gerar relatório geral: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao gerar relatório geral: {str(e)}'
        }), 500

def _criar_relatorio_geral_content(escritorio, empresa, resultados_por_tributo, year, month, status):
    """
    Cria o conteúdo do relatório geral
    """
    from reportlab.platypus import Paragraph, Spacer, Table, TableStyle, Image, PageBreak
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import mm
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_CENTER
    import os

    styles = getSampleStyleSheet()
    story = []

    # Estilo personalizado para título
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=20,
        alignment=TA_CENTER,
        textColor=colors.darkblue
    )

    # Estilo para subtítulo
    subtitle_style = ParagraphStyle(
        'CustomSubtitle',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=15,
        alignment=TA_CENTER
    )

    # Cabeçalho com logo e informações do escritório
    header_data = []

    # Verificar se existe logo
    logo_path = None
    if escritorio and escritorio.logo_path:
        logo_full_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
                                    'front', escritorio.logo_path.lstrip('/'))
        if os.path.exists(logo_full_path):
            logo_path = logo_full_path

    if logo_path:
        try:
            logo = Image(logo_path, width=60*mm, height=40*mm)
            header_data.append([logo, f"<b>{escritorio.nome}</b><br/>Responsável: {escritorio.responsavel or 'N/A'}<br/>CNPJ: {escritorio.cnpj}"])
        except:
            header_data.append(["", f"<b>{escritorio.nome}</b><br/>Responsável: {escritorio.responsavel or 'N/A'}<br/>CNPJ: {escritorio.cnpj}"])
    else:
        header_data.append(["", f"<b>{escritorio.nome}</b><br/>Responsável: {escritorio.responsavel or 'N/A'}<br/>CNPJ: {escritorio.cnpj}"])

    header_table = Table(header_data, colWidths=[60*mm, 150*mm])
    header_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('FONTSIZE', (1, 0), (1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 20),
    ]))
    story.append(header_table)

    # Título do relatório
    titulo = "RELATÓRIO GERAL DE AUDITORIA FISCAL"
    story.append(Paragraph(titulo, title_style))

    # Informações da empresa e período
    info_text = f"<b>Empresa:</b> {empresa.razao_social}<br/><b>CNPJ:</b> {empresa.cnpj}"
    if year and month:
        info_text += f"<br/><b>Período:</b> {month:02d}/{year}"
    if status != 'todos':
        info_text += f"<br/><b>Status:</b> {status.title()}"

    story.append(Paragraph(info_text, subtitle_style))
    story.append(Spacer(1, 20))

    # Resumo por tributo
    resumo_data = [['Tributo', 'Quantidade', 'Valor Total Nota', 'Valor Total Cenário', 'Diferença Total']]

    for tipo_tributo in ['icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal']:
        if tipo_tributo in resultados_por_tributo:
            resultados = resultados_por_tributo[tipo_tributo]
            quantidade = len(resultados)
            valor_total_nota = sum(float(r.valor_nota or 0) for r in resultados)
            valor_total_cenario = sum(float(r.valor_calculado or 0) for r in resultados)
            diferenca_total = valor_total_cenario - valor_total_nota

            resumo_data.append([
                tipo_tributo.upper().replace('_', '-'),
                str(quantidade),
                f"R$ {valor_total_nota:,.2f}",
                f"R$ {valor_total_cenario:,.2f}",
                f"R$ {diferenca_total:,.2f}"
            ])

    resumo_table = Table(resumo_data)
    resumo_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 9),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
    ]))

    story.append(Paragraph("RESUMO POR TRIBUTO", styles['Heading2']))
    story.append(resumo_table)
    story.append(PageBreak())

    # Detalhamento por tributo
    for tipo_tributo, resultados in resultados_por_tributo.items():
        story.append(Paragraph(f"DETALHAMENTO - {tipo_tributo.upper().replace('_', '-')}", styles['Heading2']))
        story.append(Spacer(1, 10))

        # Criar tabela de detalhamento para este tributo
        headers = ['Nota Fiscal', 'Cliente', 'Produto', 'NCM', 'CFOP', 'CST',
                   'Base Cálculo Nota', 'Alíquota Nota', 'Valor Nota',
                   'Base Cálculo Cenário', 'Alíquota Cenário', 'Valor Cenário', 'Diferença']

        table_data = [headers]

        for resultado in resultados[:50]:  # Limitar a 50 registros por tributo para não sobrecarregar
            # Buscar dados relacionados
            tributo = db.session.get(Tributo, resultado.tributo_id)
            nota_item = db.session.get(NotaFiscalItem, resultado.nota_fiscal_item_id)
            cliente = db.session.get(Cliente, tributo.cliente_id) if tributo else None
            produto = db.session.get(Produto, tributo.produto_id) if tributo else None

            # Calcular diferença
            diferenca = 0
            if resultado.valor_calculado and resultado.valor_nota:
                diferenca = float(resultado.valor_calculado) - float(resultado.valor_nota)

            row = [
                tributo.numero_nf if tributo else 'N/A',
                cliente.razao_social[:25] + '...' if cliente and len(cliente.razao_social) > 25 else (cliente.razao_social if cliente else 'N/A'),
                produto.descricao[:20] + '...' if produto and len(produto.descricao) > 20 else (produto.descricao if produto else 'N/A'),
                nota_item.ncm if nota_item else 'N/A',
                nota_item.cfop if nota_item else 'N/A',
                getattr(resultado, f'cst_nota', 'N/A'),
                f"R$ {float(resultado.base_calculo_nota):,.2f}" if resultado.base_calculo_nota else 'R$ 0,00',
                f"{float(resultado.aliquota_nota):,.2f}%" if resultado.aliquota_nota else '0,00%',
                f"R$ {float(resultado.valor_nota):,.2f}" if resultado.valor_nota else 'R$ 0,00',
                f"R$ {float(resultado.base_calculo_calculada):,.2f}" if resultado.base_calculo_calculada else 'R$ 0,00',
                f"{float(resultado.aliquota_cenario):,.2f}%" if resultado.aliquota_cenario else '0,00%',
                f"R$ {float(resultado.valor_calculado):,.2f}" if resultado.valor_calculado else 'R$ 0,00',
                f"R$ {diferenca:,.2f}"
            ]
            table_data.append(row)

        # Criar tabela
        table = Table(table_data, repeatRows=1)
        table.setStyle(TableStyle([
            # Cabeçalho
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 7),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),

            # Dados
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),

            # Zebra striping
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
        ]))

        story.append(table)

        if len(resultados) > 50:
            story.append(Spacer(1, 10))
            story.append(Paragraph(f"<i>Mostrando apenas os primeiros 50 registros de {len(resultados)} total.</i>", styles['Normal']))

        story.append(PageBreak())

    return story